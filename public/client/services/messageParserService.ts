// public/client/services/messageParserService.ts
import { Emitter } from 'mitt'; // Import Emitter
import { ToolHandlerRegistry } from './toolHandlerService';

/**
 * Represents the expected structure of the JSON data within <brief> tags.
 */

/**
 * Represents a segment of plain text content.
 */
export interface TextSegment {
  type: 'text';
  content: string;
}

/**
 * Represents the start of a recognized tool tag.
 * Supports: brief (email briefs), email (email content), plan (planning),
 * buildplan (build steps), image (single image), multiimage (multiple images),
 * ig (image generation), ie (image editing), upload (image upload), memory (memory storage)
 */
export interface ToolStartSegment {
  type: 'tool_start';
  tag: 'brief' | 'email' | 'plan' | 'buildplan' | 'image' | 'multiimage' | 'ig' | 'ie' | 'upload' | 'memory' | 'tool_message';
  placeholderId?: string; // Added to link with the specific tool invocation
}

/**
 * Represents content found within a recognized tool tag.
 * The content structure varies depending on the tag type:
 * - brief, email, plan, buildplan: JSON data
 * - image, multiimage: image references or URLs
 * - ig: image generation parameters
 * - ie: image edit parameters and base image
 * - upload: base64 image data URL
 * - memory: JSON data for memory storage
 */
export interface ToolContentSegment {
  type: 'tool_content';
  tag: 'brief' | 'email' | 'plan' | 'buildplan' | 'image' | 'multiimage' | 'ig' | 'ie' | 'upload' | 'memory' | 'tool_message';
  content: string;
}

/**
 * Represents the end of a recognized tool tag.
 * The rawContent field contains the complete content that was found between the start and end tags,
 * which can be used for additional processing if needed.
 */
export interface ToolEndSegment {
  type: 'tool_end';
  tag: 'brief' | 'email' | 'plan' | 'buildplan' | 'image' | 'multiimage' | 'ig' | 'ie' | 'upload' | 'memory' | 'tool_message';
  rawContent?: string;
  placeholderId?: string; // Added to link with the ToolStartSegment
  finalPayload?: any; // To carry the data from chat:update-segment
}

/**
 * Union type for all possible parsed segments emitted by the service.
 */
export type ParsedSegment = TextSegment | ToolStartSegment | ToolContentSegment | ToolEndSegment;

/**
 * Service to parse text streams chunk by chunk, identifying plain text
 * and segments related to specific tool tags.
 */
export class MessageParserService {
  private state: 'outside' | 'in_brief' | 'in_email' | 'in_plan' | 'in_buildplan' | 'in_image' | 'in_multiimage' | 'in_imagegen' | 'in_imageedit' | 'in_imageupload' | 'in_memory' | 'in_tool_message' = 'outside';
  private buffer: string = '';
  private tagContentBuffer: string = '';
  private currentTag: 'brief' | 'email' | 'plan' | 'buildplan' | 'image' | 'multiimage' | 'ig' | 'ie' | 'upload' | 'memory' | 'tool_message' | null = null;
  private activeTagPlaceholderId: string | null = null; // Stores ID for the currently open tag
  private emitter?: Emitter<any>; // Optional Emitter instance
  private toolFinalPayloads = new Map<string, any>(); // Stores payloads from chat:update-segment

  constructor(emitter?: Emitter<any>) {
    this.emitter = emitter;
    if (this.emitter) {
      this.emitter.on('chat:update-segment', this.handleChatUpdateSegment);
    }
  }

  private repairStreamingJson(jsonString: string): string {
	// Regular expression to find property keys missing closing quotes
	const missingQuoteRegex = /"(\w+):\s/g;
	
	// Replace with proper JSON format
	return jsonString.replace(missingQuoteRegex, '"$1": ');
  }

  private handleChatUpdateSegment = (payload: any) => {
    if (payload && payload.placeholderId) {
      // Store the entire payload, or just specific parts if preferred
      this.toolFinalPayloads.set(payload.placeholderId, payload);
      console.log(`MessageParserService: Stored payload for placeholderId ${payload.placeholderId}`);
    }
  };

  /**
   * Processes an incoming chunk of text and returns an array of parsed segments.
   * Maintains internal state to handle tags spanning multiple chunks.
   * @param chunk The text chunk to process.
   * @returns An array of ParsedSegment objects.
   */
  public processChunk(chunk: string): ParsedSegment[] {
    const segments: ParsedSegment[] = [];

    // Log the incoming chunk for debugging
    if (chunk.includes('<brief>') || chunk.includes('</brief>')) {
      console.log(`Received chunk with brief tags: '${chunk.substring(0, 50)}...'`);
    }

    this.buffer += chunk; // Append new chunk to the buffer

    // Log the buffer state after adding the chunk
    if (this.buffer.includes('<brief>') || this.buffer.includes('</brief>')) {
      console.log(`Buffer after adding chunk (length ${this.buffer.length}): '${this.buffer.substring(0, 50)}...'`);
    }

    let currentIndex = 0;
    while (currentIndex < this.buffer.length) {
      if (this.state === 'outside') {
        const briefStartIndex = this.buffer.indexOf('<brief>', currentIndex);
        const emailStartIndex = this.buffer.indexOf('<email>', currentIndex);
        const planStartIndex = this.buffer.indexOf('<plan>', currentIndex);
        const buildplanStartIndex = this.buffer.indexOf('<buildplan>', currentIndex);
        const imageStartIndex = this.buffer.indexOf('<image>', currentIndex);
        const multiimageStartIndex = this.buffer.indexOf('<multiimage>', currentIndex);
        const imagegenStartIndex = this.buffer.indexOf('<ig>', currentIndex);
        const imageeditStartIndex = this.buffer.indexOf('<ie>', currentIndex);
        const imageuploadStartIndex = this.buffer.indexOf('<upload>', currentIndex);
        const memoryStartIndex = this.buffer.indexOf('<memory>', currentIndex);
        const toolMessageStartIndex = this.buffer.indexOf('<tool_message>', currentIndex);

        let firstTagIndex = -1;
        let tag: 'brief' | 'email' | 'plan' | 'buildplan' | 'image' | 'multiimage' | 'ig' | 'ie' | 'upload' | 'memory' | 'tool_message' | null = null;
        let tagLength = 0;

        // Find the first occurring tag
        if (imageuploadStartIndex !== -1 &&
            (briefStartIndex === -1 || imageuploadStartIndex < briefStartIndex) &&
            (emailStartIndex === -1 || imageuploadStartIndex < emailStartIndex) &&
            (planStartIndex === -1 || imageuploadStartIndex < planStartIndex) &&
            (buildplanStartIndex === -1 || imageuploadStartIndex < buildplanStartIndex) &&
            (imageStartIndex === -1 || imageuploadStartIndex < imageStartIndex) &&
            (multiimageStartIndex === -1 || imageuploadStartIndex < multiimageStartIndex) &&
            (imagegenStartIndex === -1 || imageuploadStartIndex < imagegenStartIndex) &&
            (imageeditStartIndex === -1 || imageuploadStartIndex < imageeditStartIndex) &&
            (memoryStartIndex === -1 || imageuploadStartIndex < memoryStartIndex)) {
          firstTagIndex = imageuploadStartIndex;
          tag = 'upload';
          tagLength = '<upload>'.length;
        } else if (imagegenStartIndex !== -1 &&
            (briefStartIndex === -1 || imagegenStartIndex < briefStartIndex) &&
            (emailStartIndex === -1 || imagegenStartIndex < emailStartIndex) &&
            (planStartIndex === -1 || imagegenStartIndex < planStartIndex) &&
            (buildplanStartIndex === -1 || imagegenStartIndex < buildplanStartIndex) &&
            (imageStartIndex === -1 || imagegenStartIndex < imageStartIndex) &&
            (multiimageStartIndex === -1 || imagegenStartIndex < multiimageStartIndex) &&
            (imageeditStartIndex === -1 || imagegenStartIndex < imageeditStartIndex) &&
            (memoryStartIndex === -1 || imagegenStartIndex < memoryStartIndex)) {
          firstTagIndex = imagegenStartIndex;
          tag = 'ig';
          tagLength = '<ig>'.length;
        } else if (imageeditStartIndex !== -1 &&
                  (briefStartIndex === -1 || imageeditStartIndex < briefStartIndex) &&
                  (emailStartIndex === -1 || imageeditStartIndex < emailStartIndex) &&
                  (planStartIndex === -1 || imageeditStartIndex < planStartIndex) &&
                  (buildplanStartIndex === -1 || imageeditStartIndex < buildplanStartIndex) &&
                  (imageStartIndex === -1 || imageeditStartIndex < imageStartIndex) &&
                  (multiimageStartIndex === -1 || imageeditStartIndex < multiimageStartIndex)) {
          firstTagIndex = imageeditStartIndex;
          tag = 'ie';
          tagLength = '<ie>'.length;
        } else if (briefStartIndex !== -1 &&
            (emailStartIndex === -1 || briefStartIndex < emailStartIndex) &&
            (planStartIndex === -1 || briefStartIndex < planStartIndex) &&
            (buildplanStartIndex === -1 || briefStartIndex < buildplanStartIndex) &&
            (imageStartIndex === -1 || briefStartIndex < imageStartIndex) &&
            (multiimageStartIndex === -1 || briefStartIndex < multiimageStartIndex)) {
          firstTagIndex = briefStartIndex;
          tag = 'brief';
          tagLength = '<brief>'.length;
        } else if (emailStartIndex !== -1 &&
                  (briefStartIndex === -1 || emailStartIndex < briefStartIndex) &&
                  (planStartIndex === -1 || emailStartIndex < planStartIndex) &&
                  (buildplanStartIndex === -1 || emailStartIndex < buildplanStartIndex) &&
                  (imageStartIndex === -1 || emailStartIndex < imageStartIndex) &&
                  (multiimageStartIndex === -1 || emailStartIndex < multiimageStartIndex)) {
          firstTagIndex = emailStartIndex;
          tag = 'email';
          tagLength = '<email>'.length;
        } else if (planStartIndex !== -1 &&
                  (briefStartIndex === -1 || planStartIndex < briefStartIndex) &&
                  (emailStartIndex === -1 || planStartIndex < emailStartIndex) &&
                  (buildplanStartIndex === -1 || planStartIndex < buildplanStartIndex) &&
                  (imageStartIndex === -1 || planStartIndex < imageStartIndex) &&
                  (multiimageStartIndex === -1 || planStartIndex < multiimageStartIndex)) {
          firstTagIndex = planStartIndex;
          tag = 'plan';
          tagLength = '<plan>'.length;
        } else if (buildplanStartIndex !== -1 &&
                  (briefStartIndex === -1 || buildplanStartIndex < briefStartIndex) &&
                  (emailStartIndex === -1 || buildplanStartIndex < emailStartIndex) &&
                  (planStartIndex === -1 || buildplanStartIndex < planStartIndex) &&
                  (imageStartIndex === -1 || buildplanStartIndex < imageStartIndex) &&
                  (multiimageStartIndex === -1 || buildplanStartIndex < multiimageStartIndex)) {
          firstTagIndex = buildplanStartIndex;
          tag = 'buildplan';
          tagLength = '<buildplan>'.length;
        } else if (imageStartIndex !== -1 &&
                  (briefStartIndex === -1 || imageStartIndex < briefStartIndex) &&
                  (emailStartIndex === -1 || imageStartIndex < emailStartIndex) &&
                  (planStartIndex === -1 || imageStartIndex < planStartIndex) &&
                  (buildplanStartIndex === -1 || imageStartIndex < buildplanStartIndex) &&
                  (multiimageStartIndex === -1 || imageStartIndex < multiimageStartIndex)) {
          firstTagIndex = imageStartIndex;
          tag = 'image';
          tagLength = '<image>'.length;
        } else if (multiimageStartIndex !== -1) {
          console.log('Found <multiimage> tag at position', multiimageStartIndex);
          firstTagIndex = multiimageStartIndex;
          tag = 'multiimage';
          tagLength = '<multiimage>'.length;
        } else if (memoryStartIndex !== -1) {
          console.log('Found <memory> tag at position', memoryStartIndex);
          firstTagIndex = memoryStartIndex;
          tag = 'memory';
          tagLength = '<memory>'.length;
        } else if (toolMessageStartIndex !== -1) {
          console.log('Found <tool_message> tag at position', toolMessageStartIndex);
          firstTagIndex = toolMessageStartIndex;
          tag = 'tool_message';
          tagLength = '<tool_message>'.length;
        }

        if (firstTagIndex !== -1 && tag) {
          // Found a start tag
          const textBeforeTag = this.buffer.substring(currentIndex, firstTagIndex);
          if (textBeforeTag.length > 0) {
            segments.push({ type: 'text', content: textBeforeTag });
          }
          // Generate and store placeholderId for this tag instance
          this.activeTagPlaceholderId = `tool-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
          segments.push({ type: 'tool_start', tag: tag, placeholderId: this.activeTagPlaceholderId });

          // Call tool handler onStart
          const toolHandler = ToolHandlerRegistry.getInstance().getHandler(tag);
          if (toolHandler) {
            console.log(`Calling onStart for tool handler: ${tag} with placeholderId: ${this.activeTagPlaceholderId}`);
            toolHandler.onStart(this.activeTagPlaceholderId, '');
          }

          // Set the state based on the tag
          if (tag === 'ig') {
            this.state = 'in_imagegen';
          } else if (tag === 'ie') {
            this.state = 'in_imageedit';
          } else if (tag === 'upload') {
            this.state = 'in_imageupload';
          } else if (tag === 'brief') {
            this.state = 'in_brief';
          } else if (tag === 'email' ) {
            this.state = 'in_email';
          } else if (tag === 'plan' ) {
            this.state = 'in_plan';
          } else if (tag === 'buildplan' ) {
            this.state = 'in_buildplan';
          } else if (tag === 'image' ) {
            this.state = 'in_image';
          } else if (tag === 'multiimage' ) {
            console.log('Entering multiimage state with placeholderId:', this.activeTagPlaceholderId);
            this.state = 'in_multiimage';
          } else if (tag === 'memory' ) {
            console.log('Entering memory state with placeholderId:', this.activeTagPlaceholderId);
            this.state = 'in_memory';
          } else if (tag === 'tool_message' ) {
            console.log('Entering tool_message state with placeholderId:', this.activeTagPlaceholderId);
            this.state = 'in_tool_message';
          }
          this.currentTag = tag;
          this.tagContentBuffer = ''; // Reset tag content buffer
          currentIndex = firstTagIndex + tagLength;
        } else {
          // No complete start tag found in the remaining buffer
          // Check for potential incomplete tags ('<brief', '<email', etc.)
          let potentialIncompleteTag = false;
          const tagsToScan = ['<brief>', '<email>', '<plan>', '<buildplan>', '<image>', '<multiimage>', '<upload>', '<ig>', '<ie>', '<memory>', '<tool_message>'];
          let incompleteTagLength = 0;

          for (const tagStart of tagsToScan) {
              for (let i = tagStart.length - 1; i > 0; i--) {
                  if (this.buffer.endsWith(tagStart.substring(0, i))) {
                      // Found potential incomplete tag
                      incompleteTagLength = i;
                      potentialIncompleteTag = true;
                      break; // Exit inner loop
                  }
              }
              if (potentialIncompleteTag) break; // Exit outer loop
          }


          if (potentialIncompleteTag) {
              const textToEmit = this.buffer.substring(currentIndex, this.buffer.length - incompleteTagLength);
              if (textToEmit.length > 0) {
                  segments.push({ type: 'text', content: textToEmit });
              }
              // Keep the incomplete part in the buffer
              this.buffer = this.buffer.substring(this.buffer.length - incompleteTagLength);
              currentIndex = this.buffer.length; // Exit loop, wait for next chunk
          } else {
             // Emit the rest of the buffer as text if no potential tag found
             const remainingText = this.buffer.substring(currentIndex);
             if (remainingText.length > 0) {
                 segments.push({ type: 'text', content: remainingText });
             }
             this.buffer = ''; // Clear buffer as it's fully processed
             currentIndex = this.buffer.length; // Exit loop
          }
          break; // Exit while loop as we need more data or finished
        }
      } else if (this.state === 'in_brief' || this.state === 'in_email' || this.state === 'in_plan' || this.state === 'in_buildplan' ||
                this.state === 'in_image' || this.state === 'in_multiimage' || this.state === 'in_imagegen' || this.state === 'in_imageedit' || this.state === 'in_imageupload' || this.state === 'in_memory' || this.state === 'in_tool_message') {
        const currentTag = this.currentTag; // Should be 'brief', 'email', 'plan', 'buildplan', 'image', 'multiimage', 'ig', 'ie', 'upload', 'memory', or 'tool_message'
        if (!currentTag) {
            console.error("Parser error: In tag state but currentTag is null.");
            this.reset(); // Reset to avoid infinite loops
            break;
        }
        const endTag = `</${currentTag}>`;
        // Log for debugging
        //console.log(`Looking for end tag: '${endTag}' in buffer of length ${this.buffer.length}`);
        // if (this.buffer.length < 100) {
        //   console.log(`Buffer content: '${this.buffer}'`);
        // } else {
        //   console.log(`Buffer start: '${this.buffer.substring(0, 50)}...'`);
        //   console.log(`Buffer end: '...${this.buffer.substring(this.buffer.length - 50)}'`);
        // }

        const endTagIndex = this.buffer.indexOf(endTag, currentIndex);

        if (endTagIndex !== -1) {
          // Found the end tag
          console.log(`Found end tag '${endTag}' at position ${endTagIndex}`);
          const contentInsideTag = this.buffer.substring(currentIndex, endTagIndex);
          if (contentInsideTag.length > 0) {
            this.tagContentBuffer += contentInsideTag; // Add final part to buffer
            segments.push({ type: 'tool_content', tag: currentTag, content: contentInsideTag });
            console.log(`Added final content chunk of length ${contentInsideTag.length} to buffer`);
            
            // Call tool handler onContent
            const toolHandler = ToolHandlerRegistry.getInstance().getHandler(currentTag);
            if (toolHandler) {
              toolHandler.onContent(contentInsideTag);
            }
          }

          // Emit the tool_end segment
          console.log(`Emitting tool_end segment for tag '${currentTag}' with buffer length ${this.tagContentBuffer.length}`);
          segments.push({
            type: 'tool_end',
            tag: currentTag,
            rawContent: this.tagContentBuffer,
            placeholderId: this.activeTagPlaceholderId || undefined, // Ensure undefined if null
            finalPayload: this.activeTagPlaceholderId ? this.toolFinalPayloads.get(this.activeTagPlaceholderId) : undefined,
          });
          
          // Call tool handler onEnd
          const toolHandler = ToolHandlerRegistry.getInstance().getHandler(currentTag);
          if (toolHandler) {
            console.log(`Calling onEnd for tool handler: ${currentTag}`);
            toolHandler.onEnd();
          }
          
          if (this.activeTagPlaceholderId) {
            this.toolFinalPayloads.delete(this.activeTagPlaceholderId); // Clean up stored payload
            console.log(`MessageParserService: Cleared payload for placeholderId ${this.activeTagPlaceholderId}`);
          }

          // Process text immediately following the tag
          const nextIndex = endTagIndex + endTag.length;
          this.buffer = this.buffer.substring(nextIndex); // Keep only the part after the tag
          this.state = 'outside';
          this.tagContentBuffer = ''; // Clear tag content buffer
          this.currentTag = null;
          this.activeTagPlaceholderId = null; // Clear active placeholderId
          currentIndex = 0; // Process the remaining buffer from the start
        } else {
           // No end tag found in the remaining buffer
           // Check if buffer might contain an incomplete end tag at the end
           // No end tag found in the remaining buffer
           // Check for potential incomplete end tags ('</brief', '</email', etc.)
           let potentialIncompleteTag = false;
           const endTagToScan = `</${currentTag}>`;
           let incompleteTagLength = 0;

           for (let i = endTagToScan.length - 1; i > 0; i--) {
             const partialEndTag = endTagToScan.substring(0, i);
             if (this.buffer.endsWith(partialEndTag)) {
                // Found potential incomplete tag
                console.log(`Found potential incomplete end tag: '${partialEndTag}'`);
                incompleteTagLength = i;
                potentialIncompleteTag = true;
                break; // Exit inner loop
             }
           }

           // Additional check for cases where the end tag might be split across chunks
           // For example, if the buffer ends with '</' and the next chunk starts with 'brief>'
           if (!potentialIncompleteTag && this.buffer.endsWith('<')) {
             console.log("Buffer ends with '<', might be start of end tag");
             incompleteTagLength = 1;
             potentialIncompleteTag = true;
           } else if (!potentialIncompleteTag && this.buffer.endsWith('</')) {
             console.log("Buffer ends with '</', might be start of end tag");
             incompleteTagLength = 2;
             potentialIncompleteTag = true;
           }

           if (potentialIncompleteTag) {
                const contentToEmit = this.buffer.substring(currentIndex, this.buffer.length - incompleteTagLength);
                if (contentToEmit.length > 0) {
                    this.tagContentBuffer += contentToEmit;
                    segments.push({ type: 'tool_content', tag: currentTag, content: contentToEmit });
                    
                    // Call tool handler onContent
                    const toolHandler = ToolHandlerRegistry.getInstance().getHandler(currentTag);
                    if (toolHandler) {
                      toolHandler.onContent(contentToEmit);
                    }
                }
                // Keep ONLY the incomplete part in the buffer
                this.buffer = this.buffer.substring(this.buffer.length - incompleteTagLength); // Corrected: Use incompleteTagLength
                currentIndex = this.buffer.length; // Mark buffer as processed up to its (new) end
                // We found an incomplete tag, so we must wait for the next chunk. Break the while loop.
                break;
           } else {
                // No potential incomplete end tag found. Emit the rest of the buffer as content.
                const remainingContent = this.buffer.substring(currentIndex);
                 if (remainingContent.length > 0) {
                    this.tagContentBuffer += remainingContent;
                    segments.push({ type: 'tool_content', tag: currentTag, content: remainingContent });
                    
                    // Call tool handler onContent
                    const toolHandler = ToolHandlerRegistry.getInstance().getHandler(currentTag);
                    if (toolHandler) {
                      toolHandler.onContent(remainingContent);
                    }
                 }
                this.buffer = ''; // Clear buffer as it's fully processed
                currentIndex = this.buffer.length; // Mark buffer as processed
                // We processed the whole buffer but didn't find the end tag. Break the while loop.
                break;
           }
           // The logic above ensures we always break if the end tag isn't found in the current buffer.
           // Therefore, the break statement previously here on line 212 is no longer needed.
        }
      }
    }
     // If we exited the loop because we ran out of buffer content (currentIndex === this.buffer.length),
     // the remaining content in `this.buffer` needs to be preserved for the next chunk.
     // If we exited because we emitted everything (`this.buffer` became ''), it's already handled.
     // If currentIndex is less than buffer length, it means we broke early (incomplete tag)
     // and the buffer already contains the part to keep.

    return segments;
  }

  /**
   * Processes a complete message string at once.
   * Resets the parser state before processing.
   * Useful for parsing historical or non-streamed messages.
   * @param message The complete message string.
   * @returns An array of ParsedSegment objects.
   */
  public processCompleteMessage(message: string): ParsedSegment[] {
    this.reset(); // Ensure clean state
    
    // Special handling for multiimage tags in complete messages
    if (message.includes('<multiimage>') && message.includes('</multiimage>')) {
      console.log('Found multiimage tags in complete message, extracting manually');
      
      const multiimageRegex = /<multiimage>([\s\S]*?)<\/multiimage>/g;
      const matches = [...message.matchAll(multiimageRegex)];
      
      if (matches.length > 0) {
        console.log(`Found ${matches.length} multiimage tags in message`);
        
        // Extract the content between the tags
        for (const match of matches) {
          const fullMatch = match[0]; // The entire match including tags
          const content = match[1]; // Just the content between tags
          
          console.log('Extracted multiimage content:', content);
          
          // Generate a placeholderId for this content
          const placeholderId = `tool-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
          
          // Store the content in toolFinalPayloads for the tool handler to use
          try {
            const jsonContent = JSON.parse(content);
            if (Array.isArray(jsonContent)) {
              this.toolFinalPayloads.set(placeholderId, {
                type: 'multiimage',
                sender: 'ai',
                images: jsonContent.map(img => ({
                  url: img.url || '',
                  name: img.name || 'Image',
                  loaded: false
                })),
                timestamp: new Date(),
                placeholderId: placeholderId
              });
              
              console.log(`Stored multiimage content for placeholderId ${placeholderId}`);
            }
          } catch (e) {
            console.error('Error parsing multiimage content:', e);
          }
          
          // Remove the multiimage tags and content from the message
          message = message.replace(fullMatch, `[Images shown here - ID: ${placeholderId}]`);
        }
      }
    }
    
    // Process the modified message as a single chunk
    return this.processChunk(message);
  }

  /**
   * Sets the placeholderId for the current tool context.
   * Call this before processing chunks related to a specific tool invocation.
   * @param id The placeholderId, or null to clear.
   */
  /**
   * Resets the internal state of the parser.
   * Call this when starting a new stream or switching contexts.
   */
  public reset(): void {
    this.state = 'outside';
    this.buffer = '';
    this.tagContentBuffer = '';
    this.currentTag = null;
    this.activeTagPlaceholderId = null; // Reset the active placeholderId
    this.toolFinalPayloads.clear(); // Clear any stored payloads
    // console.log("MessageParserService: Reset complete.");
  }

  // Call this method when the service is no longer needed to prevent memory leaks
  public destroy(): void {
    if (this.emitter) {
      this.emitter.off('chat:update-segment', this.handleChatUpdateSegment);
    }
    this.reset();
    // console.log("MessageParserService: Destroyed.");
  }
}

// Example Usage (for testing purposes, can be removed or commented out)
/*
const parser = new MessageParserService();
const chunks = [
  "This is some plain text. ",
  "<br",
  "ief>{\"subjectLine\": \"Test Subject\", ",
  "\"previewText\": \"Preview here...\", \"briefText\": \"This is the main brief content.\"}</br",
  "ief>And some more plain text.",
  "<brief>{\"subjectLine\": \"Second Brief\", \"previewText\": \"Another preview\", \"briefText\": \"More details.\"}</brief>"
];

let allSegments: ParsedSegment[] = [];
chunks.forEach(chunk => {
  console.log(`--- Processing Chunk: "${chunk}" ---`);
  const segments = parser.processChunk(chunk);
  console.log("Segments Emitted:", segments);
  allSegments = allSegments.concat(segments);
});

console.log("\n--- Final Combined Segments ---");
console.log(JSON.stringify(allSegments, null, 2));

console.log("\n--- Processing Complete Message ---");
const completeMessage = chunks.join('');
const completeSegments = parser.processCompleteMessage(completeMessage);
console.log(JSON.stringify(completeSegments, null, 2));
*/

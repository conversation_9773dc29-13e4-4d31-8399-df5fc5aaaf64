import { URL_DOMAIN } from '../utils/utils'; // Assuming utils.js exports URL_DOMAIN
import { freeTrialInfo } from './features.js';

export interface MessageContent {
  type: 'text' | 'image_url';
  text?: string;
  image_url?: {
    url: string;
    detail?: 'low' | 'high' | 'auto';
  };
}

interface StartConversationPayload {
  message: string | MessageContent[];
  promptTemplateId?: number;
  stream: boolean;
  tools?: any[];
}

interface PostMessagePayload {
  message: string | MessageContent[];
  updateSystemPrompt?: boolean;
  tools?: any[];
}

// Helper function to check if trial has ended
async function checkTrialStatus(): Promise<{ hasAccess: boolean; error?: string }> {
  try {
    const trialData = await freeTrialInfo();
    
    // User has access if they have a paid plan or trial is still in effect
    const hasAccess = trialData.hasPaidPlan || trialData.inEffect;
    
    if (!hasAccess) {
      return {
        hasAccess: false,
        error: 'Your trial has ended. Please upgrade to continue using Raleon.'
      };
    }
    
    return { hasAccess: true };
  } catch (error) {
    console.error('Error checking trial status:', error);
    // In case of error, allow access to avoid blocking legitimate users
    return { hasAccess: true };
  }
}

// Product lookup tool definition
export const productLookupTool = {
  type: 'function',
  function: {
    name: 'product_lookup',
    description: 'Look up product information from the store catalog including details such as name, price, inventory, description, and images',
    parameters: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Product ID, SKU, or search term to find products by name, description, or category'
        },
        limit: {
          type: 'integer',
          description: 'Maximum number of products to return (defaults to 1)'
        },
        include_variants: {
          type: 'boolean',
          description: 'Whether to include variant information such as sizes and colors (defaults to true)'
        }
      },
      required: ['query']
    }
  }
}

// MEtric lookup tool definition
export const metricLookupTool = {
        type: 'function',
        function: {
          name: 'metric_lookup',
          description: 'Look up metric information from athena, metrics include all relevant data included in the table for the most recent rundate',
          parameters: {
                type: 'object',
                properties: {
                  query: {
                        type: 'string',
                        description: 'Product ID, SKU, or search term to find products by name, description, or category'
                  },
                },
                required: ['query']
          }
        }
  }

// Data lookup tool definition
export const dataLookupTool = {
        type: 'function',
        function: {
          name: 'data_lookup',
          description: 'Look up arbitrary data from athena tables including all relevant information',
          parameters: {
                type: 'object',
                properties: {
                  query: {
                        type: 'string',
                        description: 'SQL query or search term to find relevant data'
                  },
                },
                required: ['query']
          }
        }
  }

// Image lookup tool definition
export const imageLookupTool = {
        type: 'function',
        function: {
          name: 'image_lookup',
          description: 'Search for brand images by AI tag and/or description. Only returns images that have been tagged for AI use.',
          parameters: {
                type: 'object',
                properties: {
                  tag: {
                        type: 'string',
                        description: 'Filter by specific image AI tag (e.g., "product", "lifestyle", "logo")'
                  },
                  description: {
                        type: 'string',
                        description: 'Search for images matching this description or containing these keywords'
                  },
                  limit: {
                        type: 'number',
                        description: 'Maximum number of images to return (default: 10)',
                        minimum: 1,
                        maximum: 20
                  }
                }
          }
        }
  }

// Image edit tool definition
export const imageEditTool = {
        type: 'function',
        function: {
          name: 'image_edit',
          description: 'Edit an existing image to add or modify text on it. Automatically detects the image aspect ratio and generates 3 variations. Useful for customizing promotional images, adding sale text, or updating product information on images.',
          parameters: {
                type: 'object',
                properties: {
                  image_url: {
                        type: 'string',
                        description: 'URL of the image to edit'
                  },
                  new_text: {
                        type: 'string',
                        description: 'The new text to add or replace on the image'
                  },
                  text_to_replace: {
                        type: 'string',
                        description: 'Optional: existing text on the image to replace. If not provided, the AI will intelligently place the new text'
                  }
                },
                required: ['image_url', 'new_text']
          }
        }
  }

// --- Start Conversation (Streaming) ---
export async function startConversation(
  payload: Omit<StartConversationPayload, 'stream'>,
  includeProductLookup: boolean = true,
  includeMetricLookup: boolean = true,
  includeDataLookup: boolean = true,
  includeImageLookup: boolean = true,
  includeImageEdit: boolean = true
): Promise<ReadableStream<Uint8Array> | null> {
  // Check trial status first
  const trialCheck = await checkTrialStatus();
  if (!trialCheck.hasAccess) {
    console.error('Trial access denied:', trialCheck.error);
    throw new Error(trialCheck.error || 'Trial access denied');
  }

  const token = localStorage.getItem('token');
  if (!token) {
    console.error('No auth token found');
    // Handle appropriately - maybe redirect or throw error
    return null;
  }

  const streamPayload: StartConversationPayload = {
    ...payload,
    stream: true,
    tools: [
                ...(includeProductLookup ? ['product_lookup'] : []),
                ...(includeMetricLookup ? ['metric_lookup'] : []),
                ...(includeDataLookup ? ['data_lookup'] : []),
                ...(includeImageLookup ? ['image_lookup'] : []),
                ...(includeImageEdit ? ['image_edit'] : []),
                ...(payload.tools || [])
          ]

  };

  try {
    const response = await fetch(`${URL_DOMAIN}/chat/conversations/start`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(streamPayload),
    });

    if (!response.ok) {
      console.error('Failed to start conversation:', response.status, await response.text());
      // Handle error appropriately
      return null;
    }

    if (!response.body) {
      console.error('Response body is null');
      return null;
    }

    return response.body; // Return the stream directly

  } catch (error) {
    console.error('Error starting conversation:', error);
    return null;
  }
}

// --- Post Message to Conversation ---
export async function postMessage(
  conversationId: string,
  payload: PostMessagePayload,
  includeProductLookup: boolean = false,
  includeMetricLookup: boolean = false,
  includeDataLookup: boolean = false,
  includeImageLookup: boolean = false,
  includeImageEdit: boolean = false
): Promise<ReadableStream<Uint8Array> | null> {
  // Check trial status first
  const trialCheck = await checkTrialStatus();
  if (!trialCheck.hasAccess) {
    console.error('Trial access denied:', trialCheck.error);
    throw new Error(trialCheck.error || 'Trial access denied');
  }

  const token = localStorage.getItem('token');
  if (!token) {
    console.error('No auth token found');
    return null; // Or throw
  }

  try {
    // Include updateSystemPrompt in the payload if provided
    const requestPayload = {
      ...payload,
      stream: true, // Ensure stream is requested
      updateSystemPrompt: payload.updateSystemPrompt || false, // Default to false if not provided
          tools: [
                ...(includeProductLookup ? ['product_lookup'] : []),
                ...(includeMetricLookup ? ['metric_lookup'] : []),
                ...(includeDataLookup ? ['data_lookup'] : []),
                ...(includeImageLookup ? ['image_lookup'] : []),
                ...(includeImageEdit ? ['image_edit'] : []),
                ...(payload.tools || [])
          ]
    };

    const response = await fetch(`${URL_DOMAIN}/chat/conversations/${conversationId}/message`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestPayload),
    });

    if (!response.ok) {
      console.error('Failed to post message:', response.status, await response.text());
      // Handle error
      return null;
    }

    if (!response.body) {
      console.error('Response body is null for post message');
      return null;
    }

    return response.body; // Return the stream

  } catch (error) {
    console.error('Error posting message:', error);
    return null;
  }
}

// --- Get Conversation History ---
export async function getConversation(conversationId: string): Promise<any> { // Adjust return type based on actual API response
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('No auth token found');
    return null; // Or throw
  }

  try {
    const response = await fetch(`${URL_DOMAIN}/chat/conversations/${conversationId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Failed to get conversation:', response.status, await response.text());
      // Handle error
      return null;
    }

    return await response.json();

  } catch (error) {
    console.error('Error getting conversation:', error);
    return null;
  }
}

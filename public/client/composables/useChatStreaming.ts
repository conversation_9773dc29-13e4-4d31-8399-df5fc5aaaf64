import {ref, Ref, nextTick} from '@vue/runtime-core';
import {jsonrepair} from 'jsonrepair';
import {
	startConversation,
	postMessage,
} from '../services/chatService';
import {
	MessageParserService,
	ParsedSegment,
	TextSegment,
	ToolStartSegment,
	ToolContentSegment,
	ToolEndSegment,
} from '../services/messageParserService';
import {ImageProcessingService} from '../services/imageProcessService';
import {initializeToolHandlers, toolEvents} from '../services/tools';
// import * as Utils from '../../../client-old/utils/Utils'; // For URL_DOMAIN if used directly

// Import the ChatMessageSegment interface from messageArtifactService.ts
import { ChatMessageSegment } from '../services/messageArtifactService';

export interface UseChatStreamingParams {
	// Reactive state passed from parent
	conversationId: Ref<string | number | null>;
	chatSegments: Ref<ChatMessageSegment[]>;
	campaignId: Ref<string>;
	currentBriefText: Ref<string>;
	isFirstMessageAfterLoad?: Ref<boolean>;

	// Callbacks for parent component methods/actions
	onScrollToBottom: () => void;
	generateImageApi?: (params: any, placeholderId: string) => Promise<void>;
	editImageApi?: (params: any, placeholderId: string) => Promise<void>;
	handleImageUploadFromTokenApi?: (base64DataUrl: string, style?: string) => Promise<string | undefined>;
	generateEmailFromComponentsApi?: (componentJSONText: string, placeholderId: string | null) => Promise<any>;
	// For plan artifact creation after parsing
	setGeneratedPlanData?: (data: any) => void;
	// For build plan trigger - this might be simplified if build plan trigger is just a segment type
	// handleBuildPlanTriggerApi: () => Promise<void>;

	// Brief and Email specific callbacks (optional for backward compatibility)
	onBriefStreamComplete?: () => void;
	onEmailStreamComplete?: () => void;
	onUpdateBrief?: (briefData: any) => void;
	onUpdateEmail?: (emailData: any) => void;
	onAutoSaveBrief?: (briefData: any) => void;
	onAutoSaveEmail?: (emailData: any) => void;
	onGenerateEmailFromComponents?: (componentJSONText: string) => void;
	onUpdateRawBriefStream?: (rawContent: string) => void;
	onUpdateRawEmailStream?: (rawContent: string) => void;
}

export function useChatStreaming(params: UseChatStreamingParams) {
	const {
		conversationId, // Ref from parent
		chatSegments,   // Ref from parent, will be mutated
		campaignId,
		currentBriefText,
		isFirstMessageAfterLoad,
		onScrollToBottom,
		generateImageApi,
		editImageApi,
		handleImageUploadFromTokenApi,
		generateEmailFromComponentsApi,
		setGeneratedPlanData,
		// Brief and Email specific callbacks
		onBriefStreamComplete,
		onEmailStreamComplete,
		onUpdateBrief,
		onUpdateEmail,
		onAutoSaveBrief,
		onAutoSaveEmail,
		onGenerateEmailFromComponents,
		onUpdateRawBriefStream,
		onUpdateRawEmailStream,
	} = params;

	const isWaitingForAI = ref(false);
	const messageParserService = new MessageParserService();
	const segmentIdCounter = ref(0);

	// Internal state for stream processing
	const currentBriefPlaceholderId = ref<string | null>(null);
	const streamingBriefContent = ref('');
	const finalBrief = ref<string | null>(null); // Exposed
	// Additional brief state for enhanced functionality
	const rawStreamingBrief = ref('');
	const streamingEmailContent = ref('');
	const rawStreamingEmail = ref('');

	// Email assembly data is managed by parent components
	const emailAssemblyBuffer = ref('');
	const currentEmailPlaceholderId = ref<string | null>(null);
	// generatedEmailDesign will be managed by parent via generateEmailFromComponentsApi callback

	const currentPlanPlaceholderId = ref<string | null>(null);
	const streamingPlanContent = ref(''); // Exposed for artifact drawer
	const planAssemblyBuffer = ref('');
	// generatedPlanData will be managed by parent via setGeneratedPlanData callback

	const currentPlanHasParseError = ref(false); // Exposed
	const currentPlanRawContent = ref('');   // Exposed

	const currentImageGenPlaceholderId = ref<string | null>(null);
	const imageGenBuffer = ref('');
	const isGeneratingImage = ref(false); // Exposed

	const currentImageEditPlaceholderId = ref<string | null>(null);
	const imageEditBuffer = ref('');
	const isEditingImage = ref(false); // Exposed

	const currentImageUploadPlaceholderId = ref<string | null>(null);
	const imageUploadBuffer = ref(''); // Not directly used, but implies a process

	const currentMemoryPlaceholderId = ref<string | null>(null);
	const memoryBuffer = ref('');

	// Exposed state for parent component
	const isGeneratingEmail = ref(false);
	const isGeneratingPlan = ref(false);


	// Initialize tool handlers when the composable is first used
	initializeToolHandlers();

	const generateUniqueId = (): string => {
		segmentIdCounter.value++;
		return `seg-${Date.now()}-${segmentIdCounter.value}`;
	};

	const cleanContentForJsonParse = (content: string): string => {
		const tagsToClean = ['ig', 'ie', 'upload', 'image', 'multiimage', 'brief', 'email', 'plan', 'buildplan', 'memory'];
		let cleanedContent = content;
		for (const tag of tagsToClean) {
			const startTag = new RegExp(`<${tag}>`, 'g');
			const endTag = new RegExp(`</${tag}>`, 'g');
			cleanedContent = cleanedContent.replace(startTag, '').replace(endTag, '');
		}
		return cleanedContent.trim();
	};

	const parseJsonSafely = (content: string): any => {
		try {
			const cleanedContent = cleanContentForJsonParse(content);
			return JSON.parse(cleanedContent);
		} catch (firstError) {
			try {
				const cleanedContent = cleanContentForJsonParse(content);
				const repairedJson = jsonrepair(cleanedContent);
				return JSON.parse(repairedJson);
			} catch (secondError) {
				const jsonStartIndex = content.indexOf('{');
				const jsonEndIndex = content.lastIndexOf('}');
				if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
					const jsonContent = content.substring(jsonStartIndex, jsonEndIndex + 1);
					try {
						return JSON.parse(jsonContent);
					} catch (e) {
						throw new Error('Failed to parse JSON after all attempts');
					}
				} else {
					throw new Error('No valid JSON structure found in content');
				}
			}
		}
	};

	// Enhanced brief parsing with multiple fallback strategies (from BriefChat)
	const parseEnhancedBrief = (contentToProcess: string, toolEndRawContent?: string): { success: boolean; data?: any; error?: string } => {
		let extractedJson = '';
		let parseError = false;

		// Use raw content from tool_end if available, otherwise use accumulated content
		const sourceContent = (toolEndRawContent && toolEndRawContent.trim()) ? toolEndRawContent : contentToProcess;

		// Clean up the content by removing any leading colons or other non-tag characters
		const cleanedContent = sourceContent.replace(/^[^<]*(<brief>)/i, '$1');

		// Approach 1: Try to extract content between <brief> tags
		const tagRegex = /<brief>\s*([\s\S]*?)\s*<\/brief>/i;
		const match = cleanedContent.match(tagRegex);

		// Approach 2: Look for JSON object pattern in the entire content
		const jsonRegex = /\{[\s\S]*?"subjectLine"[\s\S]*?"previewText"[\s\S]*?"briefText"[\s\S]*?\}/g;
		const jsonMatches = cleanedContent.match(jsonRegex);

		// Approach 3: Direct JSON extraction
		let directJsonMatch: string | null = null;
		try {
			const jsonStartIndex = cleanedContent.indexOf('{');
			const jsonEndIndex = cleanedContent.lastIndexOf('}');
			if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
				directJsonMatch = cleanedContent.substring(jsonStartIndex, jsonEndIndex + 1);
			}
		} catch (e) {
			directJsonMatch = null;
		}

		// Select the best extraction method
		if (match && match[1] && match[1].trim()) {
			extractedJson = match[1].trim();
		} else if (jsonMatches && jsonMatches.length > 0) {
			extractedJson = jsonMatches.reduce((longest, current) => current.length > longest.length ? current : longest, '');
		} else if (directJsonMatch) {
			extractedJson = directJsonMatch;
		} else {
			const trimmedContent = sourceContent.trim();
			if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
				extractedJson = trimmedContent;
			} else {
				parseError = true;
			}
		}

		// Parse the extracted JSON with multiple fallback strategies
		if (!parseError && extractedJson) {
			try {
				// First attempt: direct parsing
				const parsedBrief = JSON.parse(extractedJson);
				return { success: true, data: parsedBrief };
			} catch (directParseError) {
				try {
					// Second attempt: jsonrepair
					const repairedJson = jsonrepair(extractedJson);
					const parsedBrief = JSON.parse(repairedJson);
					return { success: true, data: parsedBrief };
				} catch (repairError) {
					return { success: false, error: 'Failed to parse brief JSON after repair attempts' };
				}
			}
		}

		return { success: false, error: 'Failed to extract valid JSON content from brief' };
	};

	// Enhanced email parsing with fallback strategies (from BriefChat)
	const parseEnhancedEmail = (contentToProcess: string, toolEndRawContent?: string): { success: boolean; data?: any; error?: string } => {
		let extractedJson = '';
		let parseError = false;

		// Use raw content from tool_end if available, otherwise use accumulated content
		const sourceContent = (toolEndRawContent && toolEndRawContent.trim()) ? toolEndRawContent : contentToProcess;

		// Clean up the content by removing any leading non-tag characters
		const cleanedContent = sourceContent.replace(/^[^<]*(<email>)/i, '$1');

		// Extract content between <email> tags
		const tagRegex = /<email>\s*([\s\S]*?)\s*<\/email>/i;
		const match = cleanedContent.match(tagRegex);

		if (match && match[1] && match[1].trim()) {
			extractedJson = match[1].trim();
		} else {
			// Try to find JSON directly
			const jsonStartIndex = cleanedContent.indexOf('{');
			const jsonEndIndex = cleanedContent.lastIndexOf('}');

			if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
				extractedJson = cleanedContent.substring(jsonStartIndex, jsonEndIndex + 1);
			} else {
				parseError = true;
			}
		}

		// Parse the extracted JSON
		if (!parseError && extractedJson) {
			try {
				// Try direct JSON parsing
				const parsedEmail = JSON.parse(extractedJson);
				return { success: true, data: parsedEmail };
			} catch (directParseError) {
				try {
					// Try with jsonrepair
					const repairedJson = jsonrepair(extractedJson);
					const parsedEmail = JSON.parse(repairedJson);
					return { success: true, data: parsedEmail };
				} catch (repairError) {
					return { success: false, error: 'Failed to parse email JSON after repair attempts' };
				}
			}
		}

		return { success: false, error: 'Failed to extract valid JSON content from email' };
	};

	const findCompleteImageUrl = (imageRef: string): string => {
		if (!imageRef) return '';
		const imgIndexMatch = imageRef.match(/^#img(\d+)$/);
		if (imgIndexMatch) {
			const targetIndex = parseInt(imgIndexMatch[1]);
			let currentImageIndex = 0;
			for (const segment of chatSegments.value) {
				if ((segment.type === 'image' || segment.type === 'generatedImage') && segment.imageUrl) {
					if (currentImageIndex === targetIndex) return segment.imageUrl;
					currentImageIndex++;
				}
			}
			console.warn(`Image reference #img${targetIndex} not found in chat history`);
			return imageRef;
		}
		if (imageRef.endsWith('...')) {
			for (const segment of chatSegments.value) {
				if ((segment.type === 'image' || segment.type === 'generatedImage') && segment.imageUrl) {
					if (segment.imageUrl.startsWith('data:')) {
						const truncatedBase = imageRef.slice(0, -3);
						if (segment.imageUrl.startsWith(truncatedBase)) return segment.imageUrl;
					}
				}
			}
		}
		return imageRef;
	};


	const updateChatSegmentsInternal = async (parsedSegments: ParsedSegment[]) => {
		let firstContentSegmentProcessed = false;

		for (const segment of parsedSegments) {
			if (!firstContentSegmentProcessed && (segment.type === 'text' || segment.type === 'tool_start')) {
				firstContentSegmentProcessed = true;
			}

			const lastChatSegment = chatSegments.value.length > 0 ? chatSegments.value[chatSegments.value.length - 1] : null;

			switch (segment.type) {
				case 'text':
					if (isGeneratingEmail.value) break;
					const textContent = (segment as TextSegment).content;
					const processedContent = ImageProcessingService.processInlineImageTags(textContent);

					if (processedContent.hasImageTags && processedContent.imageUrls.length > 0) {
						for (const imageUrl of processedContent.imageUrls) {
							if (imageUrl) {
								chatSegments.value.push({
									id: generateUniqueId(), type: 'image', sender: 'ai', imageUrl: imageUrl,
									timestamp: new Date(), imageLoaded: false, imageError: false
								});
							}
						}
					}
					if (!processedContent.hasImageTags || processedContent.modifiedContent.trim()) {
						const contentToAdd = processedContent.hasImageTags ? processedContent.modifiedContent.trim() : textContent;
						if (lastChatSegment && lastChatSegment.type === 'text' && lastChatSegment.sender === 'ai') {
							lastChatSegment.content += contentToAdd;
						} else if (contentToAdd.trim()) {
							chatSegments.value.push({
								id: generateUniqueId(), type: 'text', sender: 'ai', content: contentToAdd, timestamp: new Date()
							});
						}
					}
					break;

				case 'tool_start':
					const toolStart = segment as ToolStartSegment;
					// Simplified logic for placeholders, actual API calls are delegated
					if (toolStart.tag === 'upload') {
						const placeholderId = generateUniqueId();
						chatSegments.value.push({
							id: placeholderId, type: 'image', sender: 'ai', isGenerating: true, content: 'Uploading image...', timestamp: new Date()
						});
						currentImageUploadPlaceholderId.value = placeholderId;
						imageUploadBuffer.value = '';
					} else if (toolStart.tag === 'ig') {
						const placeholderId = generateUniqueId();
						chatSegments.value.push({
							id: placeholderId, type: 'generatedImage', sender: 'ai', isGenerating: true, timestamp: new Date()
						});
						currentImageGenPlaceholderId.value = placeholderId;
						imageGenBuffer.value = '';
						isGeneratingImage.value = true;
					} else if (toolStart.tag === 'ie') {
						const placeholderId = generateUniqueId();
						chatSegments.value.push({
							id: placeholderId, type: 'image', sender: 'ai', isGenerating: true, timestamp: new Date()
						});
						currentImageEditPlaceholderId.value = placeholderId;
						imageEditBuffer.value = '';
						isEditingImage.value = true;
					} else if (toolStart.tag === 'brief') {
						const placeholderId = generateUniqueId();
						chatSegments.value.push({
							id: placeholderId, type: 'brief_placeholder', sender: 'ai', isGenerating: true, timestamp: new Date()
						});
						currentBriefPlaceholderId.value = placeholderId;
						streamingBriefContent.value = '';
						finalBrief.value = null;

						toolEvents.emit('artifact:open', {view: 'brief', autoOpen: true});
						// Parent component should handle opening artifact drawer
					} else if (toolStart.tag === 'email') {
						isGeneratingEmail.value = true;
						emailAssemblyBuffer.value = '';
						// Parent component should handle generatedEmailDesign = null and opening artifact drawer
						const placeholderId = generateUniqueId();
						chatSegments.value.push({
							id: placeholderId, type: 'email_placeholder', sender: 'ai', isGenerating: true, timestamp: new Date()
						});
						currentEmailPlaceholderId.value = placeholderId;
						// Ensure the email tab is selected when opening the artifact drawer
						console.log('Emitting artifact:open with view=email');
						toolEvents.emit('artifact:open', {view: 'email', autoOpen: true});
					} else if (toolStart.tag === 'plan') {
						isGeneratingPlan.value = true;
						planAssemblyBuffer.value = '';
						streamingPlanContent.value = ''; // Reset for new plan
						// Parent component should handle generatedPlanData = null and opening artifact drawer
						const placeholderId = generateUniqueId();
						chatSegments.value.push({
							id: placeholderId, type: 'plan_placeholder', sender: 'ai', isGenerating: true, timestamp: new Date()
						});
						currentPlanPlaceholderId.value = placeholderId;

						// Emit an event to notify parent to open artifact drawer
						toolEvents.emit('artifact:open', {view: 'plan', autoOpen: true});
					} else if (toolStart.tag === 'buildplan') {
						const buildPlanId = generateUniqueId();
						chatSegments.value.push({
							id: buildPlanId, type: 'buildplan_trigger', sender: 'ai', isGenerating: true, timestamp: new Date()
						});
						// currentBuildPlanPlaceholderId.value = buildPlanId; // Parent handles trigger
					} else if (toolStart.tag === 'image') {
						const imageId = toolStart.placeholderId || generateUniqueId();
						chatSegments.value.push({
							id: imageId, type: 'image', sender: 'ai', isGenerating: true, content: 'Loading image...', timestamp: new Date()
						});
					} else if (toolStart.tag === 'multiimage') {
						const multiImageId = toolStart.placeholderId || generateUniqueId();
						chatSegments.value.push({
							id: multiImageId, type: 'multiimage', sender: 'ai', images: [], timestamp: new Date()
						});
					} else if (toolStart.tag === 'memory') {
						const memoryId = toolStart.placeholderId || generateUniqueId();
						chatSegments.value.push({
							id: memoryId, type: 'memory', sender: 'ai', isGenerating: true, timestamp: new Date()
						});
						currentMemoryPlaceholderId.value = memoryId;
						memoryBuffer.value = '';
					} else if (toolStart.tag === 'tool_message') {
						const toolMessageId = toolStart.placeholderId || generateUniqueId();
						chatSegments.value.push({
							id: toolMessageId, type: 'tool_message', sender: 'ai', isGenerating: true, content: '', timestamp: new Date()
						});
					}
					break;

				case 'tool_content':
					const toolContent = segment as ToolContentSegment;
					if (toolContent.tag === 'ig' && currentImageGenPlaceholderId.value) {
						imageGenBuffer.value += toolContent.content;
					} else if (toolContent.tag === 'ie' && currentImageEditPlaceholderId.value) {
						imageEditBuffer.value += toolContent.content;
					} else if (toolContent.tag === 'brief' && currentBriefPlaceholderId.value) {
						streamingBriefContent.value += toolContent.content;
						rawStreamingBrief.value += toolContent.content;
						// Emit raw brief stream updates if callback is provided
						if (onUpdateRawBriefStream) {
							onUpdateRawBriefStream(rawStreamingBrief.value);
						}
					} else if (toolContent.tag === 'email' && isGeneratingEmail.value) {
						emailAssemblyBuffer.value += toolContent.content;
						streamingEmailContent.value += toolContent.content;
						rawStreamingEmail.value += toolContent.content;
						// Emit raw email stream updates if callback is provided
						if (onUpdateRawEmailStream) {
							onUpdateRawEmailStream(rawStreamingEmail.value);
						}
					} else if (toolContent.tag === 'plan' && isGeneratingPlan.value) {
						planAssemblyBuffer.value += toolContent.content;
						streamingPlanContent.value += toolContent.content;
					} else if (toolContent.tag === 'upload' && currentImageUploadPlaceholderId.value) { // Corrected placeholder check
						imageUploadBuffer.value += toolContent.content;
					} else if (toolContent.tag === 'memory' && currentMemoryPlaceholderId.value) {
						memoryBuffer.value += toolContent.content;
					} else if (toolContent.tag === 'image') {
						const imagePlaceholders = chatSegments.value.filter(s => s.type === 'image' && s.isGenerating === true);
						if (imagePlaceholders.length > 0) {
							const lastPlaceholder = imagePlaceholders[imagePlaceholders.length - 1];
							let currentImgContent = lastPlaceholder.content || '';
							if (currentImgContent === 'Loading image...' || !currentImgContent) {
								const content = toolContent.content.trim();
								currentImgContent = content.includes('<image>') ? content.substring(content.indexOf('<image>')) : content;
							} else {
								currentImgContent += toolContent.content.trim();
							}
							lastPlaceholder.content = currentImgContent;
						}
					} else if (toolContent.tag === 'tool_message') {
						const toolMessagePlaceholders = chatSegments.value.filter(s => s.type === 'tool_message' && s.isGenerating === true);
						if (toolMessagePlaceholders.length > 0) {
							const lastPlaceholder = toolMessagePlaceholders[toolMessagePlaceholders.length - 1];
							lastPlaceholder.content = (lastPlaceholder.content || '') + toolContent.content;
						}
					}
					break;

				case 'tool_end':
					const toolEnd = segment as ToolEndSegment;
					const lastSeg = chatSegments.value.length > 0 ? chatSegments.value[chatSegments.value.length - 1] : null;

					if (toolEnd.tag === 'memory' && currentMemoryPlaceholderId.value) {
						const placeholderId = currentMemoryPlaceholderId.value;
						const contentToParse = toolEnd.rawContent || memoryBuffer.value;
						try {
							const parsedMemory = parseJsonSafely(contentToParse);
							const segmentIndex = chatSegments.value.findIndex(s => s.id === placeholderId);
							if (segmentIndex !== -1) {
								const finalSegment = {
									...chatSegments.value[segmentIndex],
									type: 'memory' as const,
									isGenerating: false,
									memory: parsedMemory,
								};
								chatSegments.value[segmentIndex] = finalSegment;
								toolEvents.emit('memory:add', finalSegment);
							}
						} catch (error) {
							console.error('Failed to parse memory JSON:', error);
							const segmentIndex = chatSegments.value.findIndex(s => s.id === placeholderId);
							if (segmentIndex !== -1) {
								chatSegments.value.splice(segmentIndex, 1, {
									id: placeholderId,
									type: 'text',
									sender: 'ai',
									content: `Error: Failed to process memory update.`,
									timestamp: new Date(),
								});
							}
						} finally {
							currentMemoryPlaceholderId.value = null;
							memoryBuffer.value = '';
						}
					} else if (toolEnd.tag === 'upload' && currentImageUploadPlaceholderId.value && lastSeg && lastSeg.id === currentImageUploadPlaceholderId.value) {
						try {
							const imageDataUrl = findCompleteImageUrl(toolEnd.rawContent || imageUploadBuffer.value);
							if (!handleImageUploadFromTokenApi) throw new Error("handleImageUploadFromTokenApi not provided");
							const imageUrl = await handleImageUploadFromTokenApi(imageDataUrl);
							if (imageUrl) {
								lastSeg.imageUrl = imageUrl;
								lastSeg.isGenerating = false;
								lastSeg.content = undefined;
								lastSeg.imageLoaded = false; // Reset for new URL
								lastSeg.imageError = false;
							} else {
								throw new Error("Image URL not returned from upload");
							}
						} catch (error) {
							console.error('Failed to upload image:', error);
							lastSeg.content = 'Failed to upload image';
							lastSeg.isGenerating = false;
							lastSeg.imageError = true;
						}
						imageUploadBuffer.value = '';
						currentImageUploadPlaceholderId.value = null;
					} else if (toolEnd.tag === 'ig' && currentImageGenPlaceholderId.value) {
						try {
							if (!generateImageApi) throw new Error("generateImageApi not provided");
							const imageGenParams = parseJsonSafely(imageGenBuffer.value);
							await generateImageApi(imageGenParams, currentImageGenPlaceholderId.value);
						} catch (error) {
							console.error("Error parsing/generating image:", error);
							const idx = chatSegments.value.findIndex(s => s.id === currentImageGenPlaceholderId.value);
							if (idx !== -1) chatSegments.value.splice(idx, 1);
							chatSegments.value.push({id: generateUniqueId(), type: 'text', sender: 'ai', content: 'Error: Failed to generate image.', timestamp: new Date()});
						}
						currentImageGenPlaceholderId.value = null;
						imageGenBuffer.value = '';
						isGeneratingImage.value = false;
					} else if (toolEnd.tag === 'ie' && currentImageEditPlaceholderId.value) {
						try {
							if (!editImageApi) throw new Error("editImageApi not provided");
							const imageEditParams = parseJsonSafely(imageEditBuffer.value);
							await editImageApi(imageEditParams, currentImageEditPlaceholderId.value);
						} catch (error) {
							console.error("Error parsing/editing image:", error);
							const idx = chatSegments.value.findIndex(s => s.id === currentImageEditPlaceholderId.value);
							if (idx !== -1) chatSegments.value.splice(idx, 1);
							chatSegments.value.push({id: generateUniqueId(), type: 'text', sender: 'ai', content: 'Error: Failed to edit image.', timestamp: new Date()});
						}
						currentImageEditPlaceholderId.value = null;
						imageEditBuffer.value = '';
						isEditingImage.value = false;
					} else if (toolEnd.tag === 'brief' && currentBriefPlaceholderId.value) {
						// Emit brief stream complete callback if provided
						if (onBriefStreamComplete) {
							onBriefStreamComplete();
						}

						const placeholderIndex = chatSegments.value.findIndex(s => s.id === currentBriefPlaceholderId.value);
						if (placeholderIndex !== -1) {
							// Use enhanced brief parsing
							const parseResult = parseEnhancedBrief(streamingBriefContent.value, toolEnd.rawContent);

							if (parseResult.success && parseResult.data) {
								// Successfully parsed brief
								const briefData = parseResult.data;

								// Emit update callbacks if provided
								if (onUpdateBrief) {
									onUpdateBrief(briefData);
								}
								if (onAutoSaveBrief) {
									onAutoSaveBrief(briefData);
								}

								// Replace placeholder with historic brief card
								chatSegments.value[placeholderIndex] = {
									...chatSegments.value[placeholderIndex],
									type: 'historic_brief',
									isGenerating: false,
									content: 'Campaign Brief',
									briefData: briefData,
									rawContent: toolEnd.rawContent || streamingBriefContent.value,
									artifactType: 'brief',
								};

								finalBrief.value = streamingBriefContent.value;
							} else {
								// Failed to parse brief - show error
								chatSegments.value[placeholderIndex] = {
									...chatSegments.value[placeholderIndex],
									type: 'brief_error',
									isGenerating: false,
									errorMessage: 'AI generated brief in an invalid format',
									errorDetails: parseResult.error || 'This sometimes happens with AI, we are working to minimize this as much as possible. Please try again!',
								};
							}
						}

						// Reset brief state
						currentBriefPlaceholderId.value = null;
						rawStreamingBrief.value = '';
					} else if (toolEnd.tag === 'email' && isGeneratingEmail.value) {
						// Emit email stream complete callback if provided
						if (onEmailStreamComplete) {
							onEmailStreamComplete();
						}

						const placeholderIndex = chatSegments.value.findIndex(s => s.id === currentEmailPlaceholderId.value);
						if (placeholderIndex !== -1) {
							// Use enhanced email parsing
							const parseResult = parseEnhancedEmail(streamingEmailContent.value, toolEnd.rawContent);

							if (parseResult.success && parseResult.data) {
								const emailData = parseResult.data;

								// Check if email contains components vs complete design
								if (emailData && typeof emailData === 'object' && 'components' in emailData && Array.isArray(emailData.components)) {
									// Email contains component list, generate final email design
									const componentJSONText = JSON.stringify(emailData);

									// Replace placeholder with historic email card for components
									chatSegments.value[placeholderIndex] = {
										...chatSegments.value[placeholderIndex],
										type: 'historic_email',
										isGenerating: false,
										content: 'Email Design',
										design: emailData,
										rawContent: toolEnd.rawContent || streamingEmailContent.value,
										artifactType: 'email',
									};

									// Emit the special event to generate the email from components
									if (onGenerateEmailFromComponents) {
										onGenerateEmailFromComponents(componentJSONText);
									}
								} else {
									// This is already a complete email design
									// Emit update callbacks if provided
									if (onUpdateEmail) {
										onUpdateEmail(emailData);
									}
									if (onAutoSaveEmail) {
										onAutoSaveEmail(emailData);
									}

									// Replace placeholder with historic email card
									chatSegments.value[placeholderIndex] = {
										...chatSegments.value[placeholderIndex],
										type: 'historic_email',
										isGenerating: false,
										content: 'Email Design',
										design: emailData,
										rawContent: toolEnd.rawContent || streamingEmailContent.value,
										artifactType: 'email',
									};
								}
							} else {
								// Failed to parse email - show error
								chatSegments.value[placeholderIndex] = {
									...chatSegments.value[placeholderIndex],
									type: 'email_error',
									isGenerating: false,
									errorMessage: 'AI generated email in an invalid format',
									errorDetails: parseResult.error || 'This sometimes happens with AI, we are working to minimize this as much as possible. Please try again!',
								};
							}
						}

						// Reset email state
						emailAssemblyBuffer.value = '';
						rawStreamingEmail.value = '';
						isGeneratingEmail.value = false;
						currentEmailPlaceholderId.value = null;
					} else if (toolEnd.tag === 'plan' && isGeneratingPlan.value) {
						const rawPlanContentForParsing = planAssemblyBuffer.value; // Use accumulated buffer
						isGeneratingPlan.value = false; // Set before parsing/potential errors
						planAssemblyBuffer.value = ''; // Clear buffer

						try {
							let cleanedPlanDataStr = rawPlanContentForParsing;
							const jsonStartIndex = cleanedPlanDataStr.indexOf('{');
							const jsonEndIndex = cleanedPlanDataStr.lastIndexOf('}');
							if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
								cleanedPlanDataStr = cleanedPlanDataStr.substring(jsonStartIndex, jsonEndIndex + 1);
							}
							const repairedJson = jsonrepair(cleanedPlanDataStr); // Attempt repair
							const planDataObj = JSON.parse(repairedJson);

							currentPlanRawContent.value = repairedJson; // Store repaired JSON as raw
							currentPlanHasParseError.value = false;
							if (setGeneratedPlanData) {
								setGeneratedPlanData(planDataObj); // Update parent's generatedPlanData
							}

							if (currentPlanPlaceholderId.value) {
								const idx = chatSegments.value.findIndex(s => s.id === currentPlanPlaceholderId.value);
								if (idx !== -1) {
									chatSegments.value[idx] = {
										...chatSegments.value[idx], type: 'plan_artifact', isGenerating: false,
										planData: planDataObj, rawContent: repairedJson, hasParseError: false,
									};
								} else { /* Fallback ... */}
							} else { /* Fallback ... */}
						} catch (error) {
							console.error("Error parsing plan JSON:", error);
							currentPlanRawContent.value = rawPlanContentForParsing; // Store original content on error
							currentPlanHasParseError.value = true;
							if (setGeneratedPlanData) {
								setGeneratedPlanData(null);
							}

							if (currentPlanPlaceholderId.value) {
								const idx = chatSegments.value.findIndex(s => s.id === currentPlanPlaceholderId.value);
								if (idx !== -1) {
									chatSegments.value[idx] = {
										...chatSegments.value[idx], type: 'plan_artifact', isGenerating: false,
										rawContent: rawPlanContentForParsing, hasParseError: true, timestamp: new Date()
									};
								} else { /* Fallback ... */}
							} else { /* Fallback ... */}
						}
						// streamingPlanContent is already up-to-date
						currentPlanPlaceholderId.value = null;
					} else if (toolEnd.tag === 'buildplan') {
						const placeholderIndex = chatSegments.value.findIndex(s => s.type === 'buildplan_trigger' && s.isGenerating);
						if (placeholderIndex !== -1) {
							chatSegments.value[placeholderIndex].isGenerating = false;
							// Parent component's handleBuildPlanTrigger will be called via its own logic,
							// this segment just indicates the trigger.
						}
					} else if (toolEnd.tag === 'image') {
						const imagePlaceholders = chatSegments.value.filter(s => s.type === 'image' && s.isGenerating === true);
						if (imagePlaceholders.length > 0) {
							const lastPlaceholder = imagePlaceholders[imagePlaceholders.length - 1];
							let rawImgContent = lastPlaceholder.content || toolEnd.rawContent?.trim() || '';
							const imageUrl = ImageProcessingService.extractImageUrl(rawImgContent);
							if (imageUrl && imageUrl !== 'Loading image...') {
								lastPlaceholder.imageUrl = imageUrl;
								lastPlaceholder.isGenerating = false;
								lastPlaceholder.content = '';
								lastPlaceholder.imageLoaded = false;
								lastPlaceholder.imageError = false;
							} else {
								const idx = chatSegments.value.findIndex(s => s.id === lastPlaceholder.id);
								if (idx !== -1) chatSegments.value.splice(idx, 1);
							}
						} else if (toolEnd.rawContent?.trim()) {
							const imageUrl = toolEnd.rawContent.trim();
							chatSegments.value.push({
								id: generateUniqueId(), type: 'image', sender: 'ai', imageUrl: imageUrl,
								timestamp: new Date(), imageLoaded: false, imageError: false
							});
						}
					} else if (toolEnd.tag === 'multiimage') {
						// Multi-image content is complex and usually handled by toolEvents in original.
						// For now, we assume the full JSON is in rawContent or assembled.
						// This part might need more robust handling based on how MultiImageToolHandler works.
						if (toolEnd.rawContent && toolEnd.placeholderId) {
							const segmentIndex = chatSegments.value.findIndex(s => s.id === toolEnd.placeholderId && s.type === 'multiimage');
							if (segmentIndex !== -1) {
								try {
									let jsonContent = toolEnd.rawContent.trim();
									const arrayStartIndex = jsonContent.indexOf('[');
									const arrayEndIndex = jsonContent.lastIndexOf(']');
									if (arrayStartIndex !== -1 && arrayEndIndex !== -1 && arrayEndIndex > arrayStartIndex) {
										jsonContent = jsonContent.substring(arrayStartIndex, arrayEndIndex + 1);
									}
									const parsedImages = ImageProcessingService.parseImageJson(jsonContent, jsonrepair);
									if (Array.isArray(parsedImages) && parsedImages.length > 0) {
										chatSegments.value[segmentIndex].images = parsedImages.map(img => ({
											url: typeof img === 'string' ? img : (img.url || ''),
											name: typeof img === 'string' ? '' : (img.name || ''),
											loaded: false, error: false
										}));
									}
								} catch (e) {console.error("Could not parse multiimage content in stream end:", e);}
							}
						}
					} else if (toolEnd.tag === 'tool_message') {
						const toolMessagePlaceholders = chatSegments.value.filter(s => s.type === 'tool_message' && s.isGenerating === true);
						if (toolMessagePlaceholders.length > 0) {
							const lastPlaceholder = toolMessagePlaceholders[toolMessagePlaceholders.length - 1];
							lastPlaceholder.isGenerating = false;
						}
					}
					break;
			}
		}
		// Ensure DOM updates before scrolling
		// Use nextTick directly with await
		await nextTick();
		onScrollToBottom();
	};

	const handleStreamingEventInternal = async (data: any) => {
		if (data.conversationId && !conversationId.value) {
			conversationId.value = data.conversationId; // Update parent's ref
			// Parent component should handle URL update
		}

		if (data.content && typeof data.content === 'string') {
			const parsedStreamSegments = messageParserService.processChunk(data.content);
			await updateChatSegmentsInternal(parsedStreamSegments);
		}

		// Handle direct image data if API sends it this way (less common for streaming text)
		if (data.generatedImages) {
			for (const img of data.generatedImages) {
				chatSegments.value.push({
					id: generateUniqueId(), type: 'generatedImage', sender: 'ai', imageUrl: img.url,
					imageLoaded: false, timestamp: new Date()
				});
			}
		}
		if (data.editedImages) {
			for (const img of data.editedImages) {
				chatSegments.value.push({
					id: generateUniqueId(), type: 'generatedImage', sender: 'ai', imageUrl: img.url,
					imageLoaded: false, timestamp: new Date()
				});
			}
		}

		// Handle completion status
		if (data.status === 'complete' || data.done === true) {
			// Ensure all placeholder segments are marked as not generating
			for (const segment of chatSegments.value) {
				if (segment.isGenerating) {
					segment.isGenerating = false;
				}
			}

			// Reset all streaming state
			isGeneratingImage.value = false;
			isEditingImage.value = false;
			isGeneratingEmail.value = false;
			isGeneratingPlan.value = false;

			// Clear placeholder IDs
			currentImageGenPlaceholderId.value = null;
			currentImageEditPlaceholderId.value = null;
			currentImageUploadPlaceholderId.value = null;
			currentBriefPlaceholderId.value = null;
			currentEmailPlaceholderId.value = null;
			currentPlanPlaceholderId.value = null;

			// Emit an event to notify that streaming is complete
			toolEvents.emit('chat:streaming-complete', {
				conversationId: conversationId.value
			});
		}

		onScrollToBottom();
	};

	const processStream = async (streamPromise: Promise<ReadableStream | null>) => {
		isWaitingForAI.value = true; // Set at the beginning of stream processing
		// Reset per-stream state
		messageParserService.reset();
		currentBriefPlaceholderId.value = null;
		streamingBriefContent.value = '';
		currentPlanPlaceholderId.value = null;
		streamingPlanContent.value = ''; // Also reset this for the artifact drawer
		emailAssemblyBuffer.value = '';
		currentEmailPlaceholderId.value = null;
		planAssemblyBuffer.value = '';
		imageGenBuffer.value = '';
		currentImageGenPlaceholderId.value = null;
		imageEditBuffer.value = '';
		currentImageEditPlaceholderId.value = null;
		imageUploadBuffer.value = '';
		currentImageUploadPlaceholderId.value = null;


		const stream = await streamPromise;
		if (!stream) {
			chatSegments.value.push({
				id: generateUniqueId(), type: 'text', sender: 'ai',
				content: 'Error: Could not connect to the server.', timestamp: new Date(),
			});
			isWaitingForAI.value = false;
			onScrollToBottom();
			return;
		}

		const reader = stream.getReader();
		const decoder = new TextDecoder();
		let buffer = '';

		try {
			while (true) {
				const {value, done} = await reader.read();
				if (done) break;
				buffer += decoder.decode(value, {stream: true});
                                const lines = buffer.split('\n');
                                buffer = lines.pop() || '';
                                for (const line of lines) {
                                        if (line.trim() && line.startsWith('data: ')) {
                                                try {
                                                        const jsonData = JSON.parse(line.slice(6).trim());
                                                        await handleStreamingEventInternal(jsonData);
                                                } catch (e) {
                                                        console.error('Failed to parse SSE data:', line, e);
                                                }
                                        }
                                }
			}
		} catch (error) {
			console.error('Error reading stream:', error);
			chatSegments.value.push({
				id: generateUniqueId(), type: 'text', sender: 'ai', content: 'Error reading stream.', timestamp: new Date(),
			});
		} finally {
                        if (buffer.trim() && buffer.startsWith('data: ')) {
                                try {
                                        const jsonData = JSON.parse(buffer.slice(6).trim());
                                        await handleStreamingEventInternal(jsonData);
                                } catch (e) {
                                        console.error('Failed to parse final SSE data:', buffer, e);
                                }
                        }
			reader.releaseLock();
			messageParserService.reset(); // Final reset
			isWaitingForAI.value = false; // Crucial: set to false when stream ends or errors
			console.log("Stream finished");
			onScrollToBottom();
		}
	};

	const sendMessageAndProcessStream = async (message: string) => {
		let streamPromise: Promise<ReadableStream<any> | null>;
		const currentConvId = conversationId.value;

		if (!currentConvId) {
			const payload = {
				message: message,
				promptTemplateId: 12,
				campaignId: campaignId.value,
				context: { briefText: currentBriefText.value },
			};
			streamPromise = startConversation(payload, true, true, true, true, true);
		} else {
			const shouldUpdateSystemPrompt = isFirstMessageAfterLoad?.value || false;
			if (isFirstMessageAfterLoad?.value) {
				isFirstMessageAfterLoad.value = false; // Mutate the ref from the parent
			}
			const payload = {
				message: message,
				promptTemplateId: 12,
				context: { briefText: currentBriefText.value },
				updateSystemPrompt: shouldUpdateSystemPrompt,
			};
			streamPromise = postMessage(currentConvId.toString(), payload, true, true, true, true, true);
		}

		await processStream(streamPromise);
	};

	return {
		isWaitingForAI,
		// Exposed state for artifact drawer or other UI elements
		streamingBriefContent,
		finalBrief,
		// Expose states that parent might need for ChatArtifact or other conditional rendering
		isGeneratingEmail, // This is also used by parent to disable input
		isGeneratingPlan,  // This is also used by parent to disable input
		streamingPlanContent,
		currentPlanHasParseError,
		currentPlanRawContent,
		isGeneratingImage, // Used by parent to disable input
		isEditingImage,    // Used by parent to disable input

		processStream, // Main method to be called by parent
		sendMessageAndProcessStream,
	};
}

import {BindingScope, injectable, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {PromptContextService} from '../prompt/prompt-context.service';
import {
	MessageRepository,
	ConversationRepository,
	PromptTemplateRepository,
	PlannerCampaignRepository,
	TaskRepository
} from '../../repositories';
import {Message, MessageRole, MessageStatus} from '../../models';
import {
	MessageContent,
	RouterParams,
	StreamingChunk,
	ChatOptions,
	CompletionMessage,
	ToolCall,
	ToolResult
} from './types';
import {LLMRouterService} from './llm-router.service';

@injectable({scope: BindingScope.TRANSIENT})
export class ChatService {
	private _controller: any = null;

	constructor(
		@repository(MessageRepository)
		private messageRepository: MessageRepository,
		@repository(ConversationRepository)
		private conversationRepository: ConversationRepository,
		@repository(PromptTemplateRepository)
		private promptTemplateRepository: PromptTemplateRepository,
		@service(PromptContextService)
		private promptContextService: PromptContextService,
		@service(LLMRouterService)
		private router: LLMRouterService,
		@repository(PlannerCampaignRepository)
		private campaignRepository: PlannerCampaignRepository,
		@repository(TaskRepository)
		private taskRepository: TaskRepository,
	) { }

	setController(controller: any): void {
		this._controller = controller;
	}

	private async generateConversationName(userMessage: string | MessageContent[], routerParams: RouterParams, systemMessage?: string): Promise<string> {
		const userMessageText = Array.isArray(userMessage)
			? userMessage.map((msg: MessageContent) => msg.type === 'text' ? msg.text : '').join(' ')
			: userMessage;

		const messages = [];

		messages.push({
			role: MessageRole.SYSTEM,
			content: `You are a helpful assistant that creates short, descriptive titles for conversations.
	   Generate a very short (3-5 words) title that summarizes the conversation's topic or intent.
	   The title should be clear but concise.
	   DO NOT include "Title:" or any similar prefix in your response.
	   Just return the title text directly.`
		});

		if (systemMessage) {
			messages.push({
				role: MessageRole.USER,
				content: `This conversation has the following system context: ${systemMessage}

		Based on this context and the user's first message, generate an appropriate title.`
			});
		}

		messages.push({
			role: MessageRole.USER,
			content: `Generate a title for a conversation that starts with this message: ${userMessageText}`
		});

		let accumulatedContent = '';
		const handleChunk = async (chunk: StreamingChunk) => {
			if (chunk.content) {
				accumulatedContent += chunk.content;
			}
		};

		const response = await this.router.executeCompletion(
			messages,
			{
				...routerParams,
				stream: true,
				maxTokens: 20,
				tools: [],
				toolExecutors: undefined
			},
			handleChunk
		);

		return (response.content || accumulatedContent)
			.replace(/^["']|["']$/g, '')
			.replace(/^Title:\s*/i, '')
			.trim()
			.slice(0, 100);
	}

	async startConversation(
		userMessage: string | MessageContent[],
		options: ChatOptions,
	): Promise<{
		conversationId: number;
		messageId: number;
		completionPromise: Promise<void>;
		streamingUpdates: AsyncGenerator<string, void, unknown>;
	}> {
		let campaign;
		let task;
		let processedTemplateContent;
		let systemMessageContent;

		if (options.promptTemplateId) {
			const template = await this.promptTemplateRepository.findById(options.promptTemplateId);
			if (template && template.content) {
				if (options.taskId) {
					task = await this.taskRepository.findById(options.taskId, {
						include: ['taskSteps']
					});
				}

				if (options.campaignId && !task) {
					campaign = await this.campaignRepository.findById(options.campaignId, {
						include: [
							{
								relation: 'task',
								scope: {
									include: ['taskSteps']
								}
							}
						]
					});

					if (campaign && campaign.task) {
						task = campaign.task;
					}
				}

				let metricsData = {};

				processedTemplateContent = await this.promptContextService.replacePromptTags(
					template.content,
					options.organizationId,
					undefined,
					campaign,
					task,
					metricsData
				);

				systemMessageContent = processedTemplateContent;
			}
		}

		const name = await this.generateConversationName(userMessage, options.routerParams, systemMessageContent);

		const conversation = await this.conversationRepository.create({
			name,
			promptTemplateId: options.promptTemplateId,
			organizationId: options.organizationId,
			createdAt: new Date(),
			updatedAt: new Date(),
			isArchived: false,
			taskId: task?.id,
			campaignId: options.campaignId
		});

		if (!conversation.id) {
			throw new Error('Failed to create conversation: No ID generated');
		}

		const messages: Message[] = [];

		if (processedTemplateContent) {
			const systemMessage = await this.messageRepository.create({
				conversationId: conversation.id,
				role: MessageRole.SYSTEM,
				content: processedTemplateContent,
				status: MessageStatus.COMPLETED,
				createdAt: new Date(),
				updatedAt: new Date(),
			});
			messages.push(systemMessage);
		}

		const userMessageEntity = await this.messageRepository.create({
			conversationId: conversation.id,
			role: MessageRole.USER,
			content: JSON.stringify(userMessage),
			status: MessageStatus.COMPLETED,
			createdAt: new Date(),
			updatedAt: new Date(),
		});
		messages.push(userMessageEntity);

		const assistantMessage = await this.messageRepository.create({
			conversationId: conversation.id,
			role: MessageRole.ASSISTANT,
			content: '',
			status: MessageStatus.STREAMING,
			createdAt: new Date(),
			updatedAt: new Date(),
		});

		if (!assistantMessage.id) {
			throw new Error('Failed to create assistant message: No ID generated');
		}

		const streamController = new StreamController(conversation.id);

		const completionPromise = this.handleCompletion(
			conversation.id,
			assistantMessage.id,
			messages,
			options.routerParams,
			streamController.onChunk.bind(streamController),
			streamController
		).catch();

		return {
			conversationId: conversation.id,
			messageId: assistantMessage.id,
			completionPromise,
			streamingUpdates: streamController.getGenerator(),
		};
	}

	async updateSystemPrompt(
		conversationId: number,
		promptTemplateId: number,
		organizationId: number,
		campaignId?: number,
		taskId?: number
	): Promise<void> {
		const conversationWithMessages = await this.conversationRepository.findById(conversationId, {
			include: [{relation: 'messages'}],
		});

		if (!conversationWithMessages.messages) {
			throw new Error('No messages found in conversation');
		}

		const systemMessage = conversationWithMessages.messages.find(
			(m: Message) => m.role === MessageRole.SYSTEM
		);

		if (!systemMessage) {
			return;
		}

		const template = await this.promptTemplateRepository.findById(promptTemplateId);
		if (!template || !template.content) {
			throw new Error(`Prompt template ${promptTemplateId} not found or has no content`);
		}

		let campaign;
		let task;

		if (taskId) {
			task = await this.taskRepository.findById(taskId, {
				include: ['taskSteps']
			});
		}

		if (campaignId && !task) {
			campaign = await this.campaignRepository.findById(campaignId, {
				include: [
					{
						relation: 'task',
						scope: {
							include: ['taskSteps']
						}
					}
				]
			});

			if (campaign && campaign.task) {
				task = campaign.task;
			}
		}

		let metricsData = {};

		const processedTemplateContent = await this.promptContextService.replacePromptTags(
			template.content,
			organizationId,
			undefined,
			campaign,
			task,
			metricsData
		);

		await this.messageRepository.updateById(systemMessage.id, {
			content: JSON.stringify(processedTemplateContent),
			updatedAt: new Date(),
		});
	}

	async continueConversationTool(
		conversationId: number,
		toolMessages: CompletionMessage[],
		routerParams: RouterParams,
		existingStreamController?: StreamController
	): Promise<{
		messageId: number;
		completionPromise: Promise<void>;
		streamingUpdates: AsyncGenerator<string, void, unknown>;
	}> {
		const conversation = await this.conversationRepository.findById(conversationId);
		if (!conversation) {
			throw new Error('Conversation not found');
		}

		for(const toolMessage of toolMessages){
			await this.messageRepository.create({
				conversationId,
				role: MessageRole.TOOL,
				content: JSON.stringify(toolMessage.content),
				status: MessageStatus.COMPLETED,
				llmMetadata: {
					toolCallId: toolMessage.toolCallId,
					name: toolMessage.name
				},
				createdAt: new Date(),
				updatedAt: new Date(),
			});
		}

		return this.continueConversationPrivate(conversationId, [], routerParams, existingStreamController);
	}

	async continueConversationPrivate(
		conversationId: number,
		additionalMessages: Message[],
		routerParams: RouterParams,
		existingStreamController?: StreamController
	): Promise<{
		messageId: number;
		completionPromise: Promise<void>;
		streamingUpdates: AsyncGenerator<string, void, unknown>;
	}> {
		const assistantMessage = await this.messageRepository.create({
			conversationId,
			role: MessageRole.ASSISTANT,
			content: '',
			status: MessageStatus.STREAMING,
			createdAt: new Date(),
			updatedAt: new Date(),
		});

		if (!assistantMessage.id) {
			throw new Error('Failed to create assistant message: No ID generated');
		}

		const streamController = existingStreamController ?? new StreamController(conversationId);

		const conversationWithMessages = await this.conversationRepository.findById(conversationId, {
			include: [{relation: 'messages'}],
		});

		if (!conversationWithMessages.messages) {
			throw new Error('No messages found in conversation');
		}

		const historyMessages = conversationWithMessages.messages.filter(
			(m: Message) => m.id !== assistantMessage.id
		);

		const messagesToUse = [...historyMessages, ...additionalMessages];

		const completionPromise = this.handleCompletion(
			conversationId,
			assistantMessage.id,
			messagesToUse,
			routerParams,
			streamController.onChunk.bind(streamController),
			streamController
		).catch();

		return {
			messageId: assistantMessage.id,
			completionPromise,
			streamingUpdates: streamController.getGenerator(),
		};
	}

	async continueConversation(
		conversationId: number,
		userMessage: string | MessageContent[],
		routerParams: RouterParams,
		updateSystemPrompt: boolean = false
	): Promise<{
		messageId: number;
		completionPromise: Promise<void>;
		streamingUpdates: AsyncGenerator<string, void, unknown>;
	}> {
		const conversation = await this.conversationRepository.findById(conversationId);
		if (!conversation) {
			throw new Error('Conversation not found');
		}

		if (routerParams.tools && routerParams.tools.length > 0) {
			console.log('Tools requested in continueConversation:', routerParams.tools);

			if (!routerParams.toolExecutors) {
				routerParams.toolExecutors = {};
			}

			if (this._controller && this._controller.toolExecutors) {
				const toolNames = routerParams.tools.map(tool =>
					typeof tool === 'string' ? tool : tool.function?.name
				).filter(Boolean);

				for (const toolName of toolNames) {
					if (toolName && !routerParams.toolExecutors[toolName] &&
						this._controller.toolExecutors[toolName]) {
						routerParams.toolExecutors[toolName] = this._controller.toolExecutors[toolName];
					}
				}
			}
		}

		if (updateSystemPrompt && conversation.promptTemplateId && conversation.organizationId) {
			await this.updateSystemPrompt(
				conversationId,
				conversation.promptTemplateId,
				conversation.organizationId,
				conversation.campaignId,
				conversation.taskId
			);
		}

		await this.messageRepository.create({
			conversationId,
			role: MessageRole.USER,
			content: JSON.stringify(userMessage),
			status: MessageStatus.COMPLETED,
			createdAt: new Date(),
			updatedAt: new Date(),
		});

		const streamController = new StreamController(conversationId);

		return this.continueConversationPrivate(conversationId, [], routerParams, streamController);
	}

  async archiveConversation(conversationId: number): Promise<void> {
	await this.conversationRepository.updateById(conversationId, {
	  isArchived: true,
	  updatedAt: new Date(),
	});
  }

  async deleteConversation(conversationId: number): Promise<void> {
	await this.messageRepository.deleteAll({conversationId});
	await this.conversationRepository.deleteById(conversationId);
  }

	async editImage(
		userPrompt: string,
		imageUrls: string[],
		maskUrl?: string,
		routerParams: RouterParams = {}
	): Promise<{
		messageId: number;
		completionPromise: Promise<void>;
	}> {
		const message = await this.messageRepository.create({
			role: MessageRole.ASSISTANT,
			content: '',
			status: MessageStatus.STREAMING,
			createdAt: new Date(),
			updatedAt: new Date(),
		});

		if (!message.id) {
			throw new Error('Failed to create message: No ID generated');
		}

		const editParams = {
			...routerParams,
			systems: ['OpenAI'],
			providers: ['OpenAI'],
			models: ['openai/gpt-image-1'],
			imageEditing: {
				imageDataUrls: imageUrls,
				maskDataUrl: maskUrl,
				prompt: userPrompt,
				size: routerParams.imageEditing?.size || '1024x1024',
				n: routerParams.imageEditing?.n || 1
			}
		};

		const completionPromise = this.handleCompletion(
			0,
			message.id,
			[await this.messageRepository.create({
				role: MessageRole.USER,
				content: userPrompt,
				status: MessageStatus.COMPLETED,
				createdAt: new Date(),
				updatedAt: new Date()
			})],
			editParams
		).catch(error => {
			throw error;
		});

		return {
			messageId: message.id,
			completionPromise
		};
	}

	async generateImage(
		userPrompt: string,
		routerParams: RouterParams
	): Promise<{
		messageId: number;
		completionPromise: Promise<void>;
	}> {
		const message = await this.messageRepository.create({
			role: MessageRole.ASSISTANT,
			content: '',
			status: MessageStatus.STREAMING,
			createdAt: new Date(),
			updatedAt: new Date(),
		});

		if (!message.id) {
			throw new Error('Failed to create message: No ID generated');
		}

		const completionPromise = this.handleCompletion(
			0,
			message.id,
			[await this.messageRepository.create({
				role: MessageRole.USER,
				content: userPrompt,
				status: MessageStatus.COMPLETED,
				createdAt: new Date(),
				updatedAt: new Date()
			})],
			routerParams,
		).catch(error => {
			throw error;
		});

		return {
			messageId: message.id,
			completionPromise
		};
	}

	private async handleCompletion(
		conversationId: number,
		messageId: number,
		history: Message[],
		routerParams: RouterParams,
		onStreamingChunk?: (chunk: StreamingChunk) => Promise<void>,
		streamController?: StreamController
	): Promise<void> {
		try {
			const processedMessages: CompletionMessage[] = [];

			let lastAssistantWithToolCalls: {
				message: CompletionMessage,
				toolCalls: ToolCall[]
			} | null = null;

			for (const m of history) {
				let content: string | MessageContent[] = safeJsonParse(m.content) || '';

				if (m.llmMetadata) {
					const metadata = typeof m.llmMetadata === 'string' ? JSON.parse(m.llmMetadata) : m.llmMetadata;
					const imageContents: MessageContent[] = Array.isArray(content) ? content : [];

					if (m.content && typeof m.content === 'string') {
						imageContents.push({
							type: 'text',
							text: m.content
						});
					}

					if (metadata.generatedImages?.length) {
						metadata.generatedImages.forEach((img: {url: string}) => {
							imageContents.push({
								type: 'image_url',
								image_url: {
									url: img.url
								}
							});
						});
					}

					if (metadata.editedImages?.length) {
						metadata.editedImages.forEach((img: {url: string}) => {
							imageContents.push({
								type: 'image_url',
								image_url: {
									url: img.url
								}
							});
						});
					}

					if (imageContents.length > 1) {
						m.role = MessageRole.USER;
						content = imageContents;
					}
				}

				const message: CompletionMessage = {
					role: m.role,
					content
				};

				if (m.role === MessageRole.ASSISTANT && m.toolCalls) {
					try {
						const toolCalls = JSON.parse(m.toolCalls) as ToolCall[];
						if (toolCalls && toolCalls.length > 0) {
							message.tool_calls = toolCalls;

							lastAssistantWithToolCalls = {
								message,
								toolCalls
							};
						}
					} catch (error) {
					}
				} else if(m.role === MessageRole.TOOL) {
					const toolCallId = (m as any).llmMetadata?.toolCallId;
					const name = (m as any).llmMetadata?.name;

					if (!lastAssistantWithToolCalls || !toolCallId || !name ||
						!lastAssistantWithToolCalls.toolCalls.some(tc => tc.id === toolCallId)) {
						continue;
					}
					if (toolCallId && name && lastAssistantWithToolCalls) {
						const matchingToolCall = lastAssistantWithToolCalls.toolCalls.find(
							tc => tc.id === toolCallId
						);

						if (matchingToolCall) {
							message.toolCallId = toolCallId;
							message.name = name;
							processedMessages.push(message);
						}
					}
					continue;
				}

				processedMessages.push(message);

				if (m.role === MessageRole.USER && lastAssistantWithToolCalls && m.toolResults) {
					try {
						const toolResults = JSON.parse(m.toolResults) as ToolResult[];
						if (toolResults && toolResults.length > 0) {
							for (const result of toolResults) {
								const matchingToolCall = lastAssistantWithToolCalls.toolCalls.find(
									tc => tc.id === result.toolCallId
								);

								if (matchingToolCall) {
									processedMessages.push({
										role: 'tool',
										content: result.output,
										toolCallId: result.toolCallId,
										name: matchingToolCall.function.name
									});
								}
							}

							lastAssistantWithToolCalls = null;
						}
					} catch (error) {
					}
				}
			}

			const messages = processedMessages;

			let accumulatedContent = '';
			const requestStartTime = Date.now();
			let firstChunkTime: number | undefined;
			let numChunks = 0;
			let currentToolCalls: any[] = [];
			let currentToolResults: any[] = [];
			let isFirstChunk = true;

			await this.messageRepository.updateById(messageId, {
				requestStartedAt: Math.round(requestStartTime),
				isStreamed: Boolean(onStreamingChunk),
			});

			const handleChunk = async (chunk: StreamingChunk) => {
				if (chunk.content !== undefined && chunk.content !== null) {
					numChunks++;

					if (isFirstChunk) {
						isFirstChunk = false;
						if (process.env.DEBUG_STREAMING === 'true') {
							console.log(`[ChatService] FIRST CHUNK: "${chunk.content}" (length: ${chunk.content.length})`);
						}
					}

					accumulatedContent += chunk.content;

					if (process.env.DEBUG_STREAMING === 'true') {
						console.log(`[ChatService] Chunk ${numChunks}: "${chunk.content}" (accumulated: "${accumulatedContent.substring(0, 50)}..." length: ${accumulatedContent.length})`);
					}

					if (!firstChunkTime) {
						firstChunkTime = Date.now();
						await this.messageRepository.updateById(messageId, {
							timeToFirstChunk: Math.round(firstChunkTime - requestStartTime),
						});
					}
				}

				if (chunk.toolCalls?.length) {
					currentToolCalls = [...currentToolCalls, ...chunk.toolCalls];
				}
				if (chunk.toolResults?.length) {
					currentToolResults = [...currentToolResults, ...chunk.toolResults];
				}

				const updateData: Partial<Message> = {
					updatedAt: new Date(),
				};

				if (chunk.content) {
					updateData.content = accumulatedContent;
					updateData.status = chunk.done ? MessageStatus.COMPLETED : MessageStatus.STREAMING;
					updateData.numberOfChunks = numChunks;
				}

				if (chunk.done) {
					updateData.totalCompletionTime = Math.round(Date.now() - requestStartTime);
				}

				if (chunk.metadata) {
					updateData.llmSystem = chunk.metadata.system;
					updateData.llmProvider = chunk.metadata.provider;
					updateData.llmModel = chunk.metadata.model;
					updateData.cost = chunk.metadata.totalCost;
					updateData.tokenUsage = {
						promptTokens: chunk.metadata.promptTokens || 0,
						completionTokens: chunk.metadata.completionTokens || 0,
						totalTokens: chunk.metadata.totalTokens || 0,
					};
					updateData.llmMetadata = {
						tokenRatePerSec: chunk.metadata.tokenRatePerSec,
						firstTokenMs: chunk.metadata.firstTokenMs,
						latencyMs: chunk.metadata.latencyMs,
						generationTimeMs: chunk.metadata.generationTimeMs,
						finishReason: chunk.metadata.finishReason,
						...chunk.metadata.additional,
					};
				}

				if (currentToolCalls.length > 0) {
					updateData.toolCalls = JSON.stringify(currentToolCalls);
				}
				if (currentToolResults.length > 0) {
					updateData.toolResults = JSON.stringify(currentToolResults);
				}

				await this.messageRepository.updateById(messageId, updateData);

				if (chunk.done) {
					await this.updateConversationStats(conversationId);
				}

				if (onStreamingChunk) {
					await onStreamingChunk(chunk);
				}
			};
			const finalParams = {
				...routerParams,
				tools: routerParams.tools || [],
				toolExecutors: routerParams.toolExecutors || {}
			};

			const response = await this.router.executeCompletion(messages, finalParams, handleChunk);

			if (Array.isArray(response?.toolResults) && response.toolResults.length > 0) {
				const toolMessages = [];

				if (response.toolCalls && response.toolCalls.length > 0) {
					const toolCallMap = new Map<string, ToolCall>();
					for (const toolCall of response.toolCalls) {
						if (toolCall.id && toolCall.function && toolCall.function.name) {
							toolCallMap.set(toolCall.id, toolCall);
						}
					}

					for (const toolResult of response.toolResults) {
						if (toolResult.toolCallId) {
							const toolCall = toolCallMap.get(toolResult.toolCallId);
							if (toolCall && toolCall.function && toolCall.function.name) {
								toolMessages.push({
									role: 'tool',
									content: toolResult.output,
									toolCallId: toolResult.toolCallId,
									name: toolCall.function.name
								} as CompletionMessage);
							}
						}
					}
				}

				const hasToolMessages = toolMessages.some(msg => msg.role === 'tool');

				if (hasToolMessages) {
					const controllerToUse = streamController;

					try {
						await this.continueConversationTool(conversationId, toolMessages, routerParams, controllerToUse);
					} catch (error) {
						await this.messageRepository.updateById(messageId, {
							content: response.content || 'Error processing tool results. Please try again.',
							status: MessageStatus.COMPLETED,
							updatedAt: new Date()
						});
					}
				}
			}

			let finalContent = accumulatedContent;
			if (numChunks === 0 && response.content) {
				finalContent = response.content;
				if (process.env.DEBUG_STREAMING === 'true') {
					console.log(`[ChatService] No chunks processed, using response.content: "${response.content.substring(0, 50)}..."`);
				}
			} else if (accumulatedContent.length === 0 && response.content) {
				console.warn(`[ChatService] WARNING: ${numChunks} chunks processed but no content accumulated! Using response.content`);
				finalContent = response.content;
			}

			if (process.env.DEBUG_STREAMING === 'true') {
				console.log(`[ChatService] Final content (first 100 chars): "${finalContent.substring(0, 100)}..." (total length: ${finalContent.length})`);
			}

			const finalUpdate: Partial<Message> = {
				content: finalContent,
				status: MessageStatus.COMPLETED,
				updatedAt: new Date(),
				totalCompletionTime: Math.round(Date.now() - requestStartTime),
				numberOfChunks: numChunks || 1,
				llmSystem: response.system,
				llmProvider: response.provider,
				llmModel: response.model,
				cost: response.totalCost,
				tokenUsage: {
					promptTokens: response.promptTokens || 0,
					completionTokens: response.completionTokens || 0,
					totalTokens: response.totalTokens || 0,
				},
				llmMetadata: {
					tokenRatePerSec: response.tokenRatePerSec,
					firstTokenMs: response.firstTokenMs,
					latencyMs: response.latencyMs,
					generationTimeMs: response.generationTimeMs,
					finishReason: response.finishReason,
					...response.additional,
				},
				toolCalls: response.toolCalls ? JSON.stringify(response.toolCalls) : undefined,
				toolResults: response.toolResults ? JSON.stringify(response.toolResults) : undefined,
			};

			await this.messageRepository.updateById(messageId, finalUpdate);
			await this.updateConversationStats(conversationId);
		} catch (error) {
			const finalUpdate: Partial<Message> = {
				content: 'An error occurred while generating the response.' + error,
				status: MessageStatus.FAILED,
				updatedAt: new Date(),
				totalCompletionTime: Math.round(Date.now() - (await this.messageRepository.findById(messageId)).requestStartedAt!),
			};
			await this.messageRepository.updateById(messageId, finalUpdate);
			throw error;
		}
	}

	private async updateConversationStats(conversationId: number): Promise<void> {
		if (!conversationId) return;

		const conversation = await this.conversationRepository.findById(conversationId, {
			include: [{relation: 'messages'}],
		});

		if (!conversation.messages) return;

		const messages = conversation.messages;
		const assistantMessages = messages.filter(m => m.role === MessageRole.ASSISTANT);
		const userMessages = messages.filter(m => m.role === MessageRole.USER);

		let totalCost = 0;
		const tokenUsage = {
			promptTokens: 0,
			completionTokens: 0,
			totalTokens: 0,
		};

		for (const message of messages) {
			if (message.cost) {
				totalCost += message.cost;
			}
			if (message.tokenUsage) {
				tokenUsage.promptTokens += message.tokenUsage.promptTokens;
				tokenUsage.completionTokens += message.tokenUsage.completionTokens;
				tokenUsage.totalTokens += message.tokenUsage.totalTokens;
			}
		}

		const completedAssistantMessages = assistantMessages.filter(m =>
			m.status === MessageStatus.COMPLETED && m.totalCompletionTime
		);

		const averageResponseTime = completedAssistantMessages.length > 0
			? Math.round(completedAssistantMessages.reduce((sum, m) => {
				const time = typeof m.totalCompletionTime === 'string'
					? parseInt(m.totalCompletionTime, 10)
					: (m.totalCompletionTime || 0);
				return sum + time;
			}, 0) / completedAssistantMessages.length)
			: undefined;

		const lastUserMessage = userMessages.length > 0
			? userMessages.reduce((latest, msg) =>
				latest.createdAt > msg.createdAt ? latest : msg
			)
			: undefined;

		const lastAssistantMessage = assistantMessages.length > 0
			? assistantMessages.reduce((latest, msg) =>
				latest.createdAt > msg.createdAt ? latest : msg
			)
			: undefined;

		await this.conversationRepository.updateById(conversationId, {
			totalCost,
			totalTokenUsage: tokenUsage,
			messageCount: messages.length,
			averageResponseTime,
			lastUserMessageAt: lastUserMessage?.createdAt,
			lastAssistantMessageAt: lastAssistantMessage?.createdAt,
			updatedAt: new Date(),
		});
	}
}

export class StreamController {
	private static instanceCount = 0;
	private instanceId: number;
	private resolveNext: ((value: IteratorResult<string, void>) => void) | null = null;
	private rejectNext: ((error: Error) => void) | null = null;
	private isDone = false;
	private error: Error | null = null;
	private buffer: string[] = [];
	private generatorCreated = false;
	private pendingToolCalls = false;
	private yieldInProgress = false;
	private conversationId?: number;
	private currentGenerator?: AsyncGenerator<string, void, unknown>;
	private isProcessing = false;

	constructor(conversationId?: number) {
		this.instanceId = ++StreamController.instanceCount;
		this.conversationId = conversationId;
	}

	reset(): void {
		this.resolveNext = null;
		this.rejectNext = null;
		this.isDone = false;
		this.error = null;
		this.buffer = [];
		this.generatorCreated = false;
		this.pendingToolCalls = false;
		this.yieldInProgress = false;
		this.isProcessing = false;
	}

	async onChunk(chunk: StreamingChunk) {
		try {
			if (process.env.DEBUG_STREAMING === 'true') {
				console.log(`[StreamController ${this.instanceId}] onChunk: content="${chunk.content}", done=${chunk.done}, bufferSize=${this.buffer.length}`);
			}

			if (chunk.error) {
				this.error = chunk.error;
				if (this.rejectNext) {
					this.rejectNext(chunk.error);
					this.rejectNext = null;
				}
				return;
			}

			if (chunk.toolCalls && chunk.toolCalls.length > 0) {
				if (process.env.DEBUG_STREAMING === 'true') {
					console.log(`[StreamController ${this.instanceId}] TOOL CALLS detected, content: "${chunk.content || ''}", length: ${(chunk.content || '').length}`);
				}
				this.pendingToolCalls = true;

				if (!chunk.content || !chunk.content.trim()) {
					const toolCall = chunk.toolCalls[0];
					const toolName = toolCall?.function?.name || 'unknown';
					let toolArgs = {};

					try {
						if (toolCall?.function?.arguments) {
							toolArgs = JSON.parse(toolCall.function.arguments);
						}
					} catch (e) {
						console.error('Error parsing tool arguments:', e);
					}

					let friendlyName = toolName;
					if (toolName === 'metric_lookup') {
						const metricLookupMessages = [
							"Reviewing your latest metrics...",
							"Gathering insights from your data...",
							"Crunching the numbers...",
							"Analyzing store performance...",
							"Pulling your key metrics...",
							"Checking your analytics dashboard...",
							"Looking up your performance data...",
							"Scanning your store metrics...",
							"Fetching reporting details...",
							"Accessing your metric history..."
						];

						const randomIndex = Math.floor(Math.random() * metricLookupMessages.length);
						friendlyName = metricLookupMessages[randomIndex];
					}
					if (toolName === 'product_lookup') {
							const productLookupMessages = [
								"Looking at your store inventory...",
								"Checking your product catalog...",
								"Searching your store products...",
								"Browsing your inventory...",
								"Looking up your product information...",
								"Checking what's in your store...",
								"Scanning your product listings...",
								"Reviewing your store catalog...",
								"Fetching product details...",
								"Accessing your store data..."
							];

							const randomIndex = Math.floor(Math.random() * productLookupMessages.length);
							friendlyName = productLookupMessages[randomIndex];
					}

					if (toolName === 'data_lookup') {
							const dataLookupMessages = [
								"Getting your data...",
								"Looking things up...",
								"Finding what you need...",
								"Checking the info...",
								"Loading your results...",
								"Hang tight, grabbing that now...",
								"Bringing it up...",
								"Working on it...",
								"One sec, pulling that for you..."
							];

							const randomIndex = Math.floor(Math.random() * dataLookupMessages.length);
							friendlyName = dataLookupMessages[randomIndex];
					}

					if (toolName === 'best_sellers') {
							const bestSellersMessages = [
								"Finding your top products...",
								"Analyzing sales data...",
								"Checking best sellers...",
								"Looking up top performers...",
								"Getting sales rankings...",
								"Pulling bestseller data...",
								"Analyzing product performance...",
								"Finding your winners...",
								"Checking what's selling..."
							];

							const randomIndex = Math.floor(Math.random() * bestSellersMessages.length);
							friendlyName = bestSellersMessages[randomIndex];
					}

					if (toolName === 'image_lookup') {
							const imageLookupMessages = [
								"Searching your brand images...",
								"Looking through your assets...",
								"Finding the perfect images...",
								"Browsing your image library...",
								"Checking your visual content...",
								"Scanning your brand assets...",
								"Looking for matching images...",
								"Reviewing your image collection...",
								"Finding relevant visuals...",
								"Searching your gallery..."
							];

							const randomIndex = Math.floor(Math.random() * imageLookupMessages.length);
							friendlyName = imageLookupMessages[randomIndex];
					}

					if (toolName === 'image_edit') {
							const imageEditMessages = [
								"Editing your image with AI...",
								"Adding text to your image...",
								"Creating image variations...",
								"Processing your image edit...",
								"Applying text modifications...",
								"Generating edited versions...",
								"Customizing your image...",
								"Working on your image design...",
								"Crafting your visual content...",
								"Enhancing your image with text...",
								"Creating your promotional image...",
								"Designing your custom visual..."
							];

							const randomIndex = Math.floor(Math.random() * imageEditMessages.length);
							friendlyName = imageEditMessages[randomIndex];
					}

					this.buffer.push(`<tool_message>${friendlyName}...</tool_message>`);
				}
			}

			if (chunk.content !== undefined && chunk.content !== null) {
				if (process.env.DEBUG_STREAMING === 'true') {
					console.log(`[StreamController ${this.instanceId}] CONTENT CHUNK: "${chunk.content}" (length: ${chunk.content.length}, bufferSize: ${this.buffer.length}, pendingToolCalls: ${this.pendingToolCalls})`);
				}

				this.buffer.push(chunk.content);

				if (chunk.content.length > 0) {
					this.pendingToolCalls = false;
				}
			}

			if (chunk.done && !this.pendingToolCalls) {
				this.isDone = true;
			}

			this.processNextChunk();
		} catch (error) {
			this.error = error instanceof Error ? error : new Error(String(error));
			if (this.rejectNext) {
				this.rejectNext(this.error);
				this.rejectNext = null;
			}
		}
	}

	private processNextChunk() {
		if (this.isProcessing) {
			return;
		}

		this.isProcessing = true;
		try {
			if (this.resolveNext && (this.buffer.length > 0 || this.isDone)) {
				if (this.buffer.length > 0) {
					const content = this.buffer.shift()!;

					const resolveFunc = this.resolveNext;
					this.resolveNext = null;

					resolveFunc({value: content, done: false});
				} else if (this.isDone) {
					const resolveFunc = this.resolveNext;
					this.resolveNext = null;

					resolveFunc({value: undefined, done: true});
				}
			}
		} finally {
			this.isProcessing = false;
			if (this.buffer.length > 0 || (this.isDone && this.resolveNext)) {
				setImmediate(() => this.processNextChunk());
			}
		}
	}

	getGenerator(): AsyncGenerator<string, void, unknown> {
		if (this.currentGenerator) return this.currentGenerator;
		this.generatorCreated = true;
		this.currentGenerator = this.generateStream();
		return this.currentGenerator;
	}

	private async *generateStream(): AsyncGenerator<string, void, unknown> {
		try {
			while (true) {
				while (this.buffer.length > 0) {
					const chunk = this.buffer.shift()!;
					this.yieldInProgress = true;
					yield chunk;
					this.yieldInProgress = false;
				}

				if (this.isDone || this.error) {
					break;
				}

				const result = await new Promise<IteratorResult<string, void>>((resolve, reject) => {
					this.resolveNext = resolve;
					this.rejectNext = reject;

					this.processNextChunk();
				});

				if (result.done) {
					break;
				}

				this.yieldInProgress = true;
				yield result.value;
				this.yieldInProgress = false;
			}

			while (this.buffer.length > 0) {
				const chunk = this.buffer.shift()!;
				this.yieldInProgress = true;
				yield chunk;
				this.yieldInProgress = false;
			}
		} catch (error) {
			throw error;
		}
	}
}

function safeJsonParse(jsonString: string): any | null {
	try {
		return JSON.parse(jsonString);
	} catch (error) {
		return jsonString;
	}
}
